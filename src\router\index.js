import { createWebHistory, createRouter } from 'vue-router';
/* Layout */
import Layout from '@/layout';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true },
      },
    ],
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: {
          title: '字典数据',
          activeMenu: '/system/dict/index',
          noCache: true,
        },
      },
    ],
  },
  {
    path: '/operation/coupons',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'details/:id',
        component: () => import('@/views/operation/coupons/details'),
        name: 'CouponsDetails',
        meta: {
          title: '优惠券详情',
          activeMenu: '/operation/coupons',
          noCache: true,
        },
      },
    ],
  },
  // 用户审核
  {
    path: '/organizational/organizational',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'audit/:id',
        component: () => import('@/views/organizational/organizational/audit'),
        name: 'OrganizationalAudit',
        meta: {
          title: '用户审核',
          activeMenu: '/organizational/organizational/index',
          noCache: true,
        },
      },
    ],
  },
  // 旧物详情
  {
    path: '/operation/recycle-repair',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'details/recycleClothes/:id',
        component: () =>
          import('@/views/operation/recycle-repair/details/recycleClothes'),
        name: 'RecycleClothesDetails',
        meta: {
          title: '旧衣物详情',
          activeMenu: '/operation/recycle-repair/index',
          noCache: true,
        },
      },
      {
        path: 'details/recycleSize/:id',
        component: () =>
          import('@/views/operation/recycle-repair/details/recycleSize'),
        name: 'RecycleSizeDetails',
        meta: {
          title: '旧物详情',
          activeMenu: '/operation/recycle-repair/index',
          noCache: true,
        },
      },
      {
        path: 'details/recycleMaintain/:id',
        component: () =>
          import('@/views/operation/recycle-repair/details/recycleMaintain'),
        name: 'RecycleMaintainDetails',
        meta: {
          title: '家庭维修详情',
          activeMenu: '/operation/recycle-repair/index',
          noCache: true,
        },
      },
    ],
  },
  // 订单详情
  {
    path: '/operation/orders-group',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'details/mallOrders/:id',
        component: () =>
          import('@/views/operation/orders-group/details/mallOrders'),
        name: 'MallOrdersDetails',
        meta: {
          title: '商城订单详情',
          activeMenu: '/operation/orders-group/index',
          noCache: true,
        },
      },
      {
        path: 'details/groupProducts/:id',
        component: () =>
          import('@/views/operation/orders-group/details/groupProducts'),
        name: 'GroupProductsDetails',
        meta: {
          title: '已发团商品详情',
          activeMenu: '/operation/orders-group/index',
          noCache: true,
        },
      },
    ],
  },
  // 合作商入驻详情
  {
    path: '/operation/cooperation',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'details/:id',
        component: () =>
          import('@/views/operation/cooperation/details/cooperationList'),
        name: 'CooperationDetail',
        meta: {
          title: '合作商入驻详情',
          activeMenu: '/operation/cooperation/index',
          noCache: true,
        },
      },
    ],
  },
  // 合作商交纳年费详情
  {
    path: '/financial',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'partnerAnnualFee/details/:id',
        component: () => import('@/views/financial/partnerAnnualFee/details'),
        name: 'PartnerAnnualFeeDetails',
        meta: {
          title: '合作商交纳年费详情',
          activeMenu: '/financial/partnerAnnualFee/index',
          noCache: true,
        },
      },
      {
        path: 'profitSharingStats/details/:id',
        component: () => import('@/views/financial/profitSharingStats/details'),
        name: 'ProfitSharingStatsDetails',
        meta: {
          title: '各级分润统计详情',
          activeMenu: '/financial/profitSharingStats/index',
          noCache: true,
        },
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

export default router;
