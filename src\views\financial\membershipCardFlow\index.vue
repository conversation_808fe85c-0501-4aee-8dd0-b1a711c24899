<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="打款状态" prop="paymentStatus">
        <el-select
          v-model="queryParams.paymentStatus"
          placeholder="请选择打款状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in payment_status_dict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="手机" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="帐号" prop="account">
        <el-input
          v-model="queryParams.account"
          placeholder="请输入帐号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网点名称" prop="outletName">
        <el-select
          v-model="queryParams.outletName"
          placeholder="请选择网点名称"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in outlet_name_dict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="驿站名称" prop="stationName">
        <el-select
          v-model="queryParams.stationName"
          placeholder="请选择驿站名称"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in station_name_dict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="membershipCardList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="(会员)帐号"
        align="center"
        prop="memberAccount"
        width="120"
      />
      <el-table-column
        label="(会员)手机号"
        align="center"
        prop="memberPhone"
        width="120"
      />
      <el-table-column
        label="(店家)帐号"
        align="center"
        prop="storeAccount"
        width="120"
      />
      <el-table-column
        label="(店家)手机号"
        align="center"
        prop="storePhone"
        width="120"
      />
      <el-table-column
        label="(店家)绑定名称"
        align="center"
        prop="storeName"
        width="200"
      />
      <el-table-column
        label="消费支付订单编号"
        align="center"
        prop="orderNo"
        width="150"
      />
      <el-table-column
        label="会员卡名称"
        align="center"
        prop="cardName"
        width="150"
      />
      <el-table-column
        label="打款金额(元)"
        align="center"
        prop="paymentAmount"
        width="120"
      >
        <template #default="scope">
          <span>￥{{ scope.row.paymentAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="支付/打款方式"
        align="center"
        prop="paymentMethod"
        width="120"
      />
      <el-table-column
        label="打款信息"
        align="center"
        prop="paymentInfo"
        width="200"
      >
        <template #default="scope">
          <div class="payment-info">
            <div>帐户:{{ scope.row.paymentAccount }}</div>
            <div>账号:{{ scope.row.paymentAccountNo }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="核销时间"
        align="center"
        prop="verificationTime"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.verificationTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部统计信息 -->
    <div class="summary-info">
      累计核销笔数: {{ totalCount }}笔 累计核销金额: ￥{{ totalAmount }}元
    </div>
  </div>
</template>

<script setup name="MembershipCardFlow">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { getMembershipCardFlowList } from '@/api/financial';

const { proxy } = getCurrentInstance();

const membershipCardList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);
const totalCount = ref(336);
const totalAmount = ref(2235);
const dateRange = ref([]);

// 字典数据
const { payment_status_dict, outlet_name_dict, station_name_dict } =
  proxy.useDict('payment_status_dict', 'outlet_name_dict', 'station_name_dict');

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    startDate: undefined,
    endDate: undefined,
    paymentStatus: undefined,
    phone: undefined,
    account: undefined,
    outletName: undefined,
    stationName: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  getMembershipCardFlowList(queryParams.value)
    .then((response) => {
      membershipCardList.value = response.data?.records || [];
      total.value = response.data?.total || 0;
    })
    .catch((error) => {
      proxy.$modal.msgError('获取数据失败');
      console.error('获取数据失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  dateRange.value = [];
  queryParams.value.startDate = undefined;
  queryParams.value.endDate = undefined;
  queryParams.value.paymentStatus = undefined;
  queryParams.value.phone = undefined;
  queryParams.value.account = undefined;
  queryParams.value.outletName = undefined;
  queryParams.value.stationName = undefined;
  handleQuery();
}

function handleDateChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.value.startDate = dates[0];
    queryParams.value.endDate = dates[1];
  } else {
    queryParams.value.startDate = undefined;
    queryParams.value.endDate = undefined;
  }
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}
</script>

<style scoped>
.payment-info {
  font-size: 12px;
  line-height: 1.4;
  text-align: left;
}

.summary-info {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
</style>
