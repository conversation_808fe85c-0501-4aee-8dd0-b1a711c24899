import request from '@/utils/request';

// 获取优惠券列表
export function listCoupons(params) {
  return request({
    url: '/platform/coupons/list',
    method: 'get',
    params,
  });
}

// 获取优惠券详情
export function getCoupons(couponsId) {
  return request({
    url: `/platform/coupons/${couponsId}`,
    method: 'get',
  });
}

// 获取优惠券使用记录
export function getCouponsUsageList(couponsId) {
  return request({
    url: '/platform/coupon/usage/list',
    method: 'get',
    params: { couponsId },
  });
}

// 新增优惠券
export function addCoupons(data) {
  return request({
    url: '/platform/coupons/add',
    method: 'post',
    data,
  });
}

// 修改优惠券
export function updateCoupons(data) {
  return request({
    url: '/platform/coupons/edit',
    method: 'put',
    data,
  });
}

// 删除优惠券
export function delCoupons(ids) {
  return request({
    url: `/platform/coupons/${ids}`,
    method: 'delete',
  });
}
