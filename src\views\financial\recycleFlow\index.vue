<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="手机" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入昵称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="recycleFlowList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="帐号" align="center" prop="mobile" width="120" />
      <el-table-column
        label="昵称"
        align="center"
        prop="nickName"
        width="120"
      />
      <el-table-column
        label="手机号"
        align="center"
        prop="mobile"
        width="120"
      />
      <el-table-column label="网点名称" align="center" prop="websiteName" />
      <el-table-column label="驿站名称" align="center" prop="stationName" />
      <el-table-column
        label="回收订单编号"
        align="center"
        prop="orderNo"
        width="150"
      />
      <el-table-column
        label="订单总重量(公斤)"
        align="center"
        prop="weight"
        width="150"
      />
      <el-table-column label="金额(元)" align="center" prop="price" width="100">
        <template #default="scope">
          <span>￥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="完单时间"
        align="center"
        prop="completeTime"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.completeTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部统计信息 -->
    <div class="summary-info">
      累计重量: {{ totalWeight }}公斤 累计金额: ￥{{ totalAmount }}元
    </div>
  </div>
</template>

<script setup name="RecycleFlow">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { getRecycleFlowList } from '@/api/financial';

const { proxy } = getCurrentInstance();
// 统计数据
const statisticsData = ref({});
const recycleFlowList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);
const totalWeight = ref();
const totalAmount = ref();
const dateRange = ref([]);

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    startTime: undefined,
    endTime: undefined,
    mobile: undefined,
    nickName: undefined,
    outletName: undefined,
    stationName: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  getRecycleFlowList(queryParams.value)
    .then((response) => {
      recycleFlowList.value = response.data?.page?.records || [];
      totalAmount.value = response.data?.prices;
      totalWeight.value = response.data?.weight;
      total.value = response.data?.page?.total || 0;
    })
    .catch((error) => {
      proxy.$modal.msgError('获取数据失败');
      console.error('获取数据失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  dateRange.value = [];
  proxy.resetForm('queryForm');
  handleQuery();
}

function handleDateChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.value.startTime = dates[0];
    queryParams.value.endTime = dates[1];
  } else {
    queryParams.value.startTime = undefined;
    queryParams.value.endTime = undefined;
  }
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}
</script>

<style scoped>
.summary-info {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
</style>
