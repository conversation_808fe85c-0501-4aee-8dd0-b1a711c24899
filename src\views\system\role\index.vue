<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增角色</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="roleList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="排序号" align="center" prop="sort" width="80">
        <template #default="scope">
          {{
            (queryParams.pageIndex - 1) * queryParams.pageSize +
            scope.$index +
            1
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="角色编号"
        align="center"
        prop="roleId"
        width="120"
      />
      <el-table-column label="角色名称" align="center" prop="roleName" />
      <el-table-column
        label="应用权限"
        align="center"
        prop="accessNames"
        show-overflow-tooltip
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="150px"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button type="text" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button type="text" @click="handlePermission(scope.row)"
            >权限</el-button
          >
          <el-button type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改角色对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="排序号" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="999"
            controls-position="right"
            placeholder="请输入排序号"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="submitLoading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <el-dialog
      title="选择功能"
      v-model="permissionOpen"
      width="600px"
      append-to-body
    >
      <div class="permission-header">
        <span
          >角色编号：{{ currentRole.roleId }} 角色名称：{{
            currentRole.roleName
          }}</span
        >
      </div>
      <div class="permission-content">
        <div class="permission-section">
          <div class="section-title">PC管理端代理商功能</div>
          <el-tree
            ref="permissionTreeRef"
            :data="permissionList"
            node-key="accessId"
            show-checkbox
            default-expand-all
            :props="{ children: 'children', label: 'name' }"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            :loading="permissionLoading"
            @click="submitPermission"
            >确 定</el-button
          >
          <el-button @click="permissionOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Role">
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue';
import { listRole, addRole, updateRole, delRole } from '@/api/system/role';
import { listMenu } from '@/api/system/menu/pc';
import { handleTree } from '@/utils/weilian.js';
const { proxy } = getCurrentInstance();

// 响应式数据
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const roleList = ref([]);
const title = ref('');
const open = ref(false);
const permissionOpen = ref(false);
const currentRole = ref({});
const permissionList = ref([]);
const selectedPermissions = ref([]);
// 用于读取/设置树形勾选
const permissionTreeRef = ref(null);
const submitLoading = ref(false);
const permissionLoading = ref(false);
const formRef = ref();

// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  roleName: null,
});

// 表单参数
const form = reactive({
  roleId: null,
  roleName: null,
  sort: 0,
});

// 表单校验规则
const rules = reactive({
  roleName: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序号不能为空', trigger: 'blur' }],
});

// 生命周期
onMounted(() => {
  getList();
});

// 方法
/** 查询角色列表 */
function getList() {
  loading.value = true;
  listRole(queryParams)
    .then((response) => {
      roleList.value = response.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.roleId = null;
  form.roleName = null;
  form.sort = 0;
  proxy.resetForm('formRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageIndex = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.roleId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加角色';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const roleId = row.roleId || ids.value[0];
  // 这里需要根据实际情况获取角色详情
  // 暂时使用行数据
  form.roleId = row.roleId;
  form.roleName = row.roleName;
  form.sort = row.sort || 0;
  open.value = true;
  title.value = '修改角色';
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      if (form.roleId != null) {
        updateRole(form)
          .then((response) => {
            proxy.$modal.msgSuccess('修改成功');
            open.value = false;
            getList();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      } else {
        addRole(form)
          .then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const roleIds = row.roleId || ids.value;
  proxy.$modal
    .confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？')
    .then(function () {
      return delRole(roleIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 权限分配按钮操作 */
function handlePermission(row) {
  currentRole.value = row;
  selectedPermissions.value = row.platformAccessList.map(
    (item) => item.accessId
  );
  getPermissionList();
  permissionOpen.value = true;
}

/** 获取权限列表 */
function getPermissionList() {
  listMenu().then((response) => {
    permissionList.value = handleTree(response.data, 'accessId') || [];
    nextTick(() => {
      if (permissionTreeRef.value) {
        selectedPermissions.value.forEach((v) => {
          nextTick(() => {
            permissionTreeRef.value.setChecked(v, true, false);
          });
        });
      }
    });
  });
}

/** 所有部门节点数据 */
function getDeptAllCheckedKeys() {
  // 目前被选中的部门节点
  let checkedKeys = permissionTreeRef.value.getCheckedKeys();
  // 半选中的部门节点
  let halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  return checkedKeys;
}
/** 提交权限分配 */
function submitPermission() {
  permissionLoading.value = true;
  const checkedKeys = getDeptAllCheckedKeys();

  console.log('哈哈哈哈', checkedKeys);

  const data = {
    ...currentRole.value,
    roleId: currentRole.value.roleId,
    accessId: checkedKeys,
  };
  updateRole(data)
    .then((response) => {
      proxy.$modal.msgSuccess('权限分配成功');
      permissionOpen.value = false;
      getList();
    })
    .finally(() => {
      permissionLoading.value = false;
    });
}
</script>

<style scoped>
.permission-header {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-weight: bold;
}

.permission-content {
  max-height: 400px;
  overflow-y: auto;
}

.permission-section {
  margin-bottom: 20px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.el-checkbox {
  margin-right: 0;
  min-width: 120px;
}
</style>
