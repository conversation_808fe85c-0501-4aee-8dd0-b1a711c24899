<template>
  <div class="app-container">
    <div class="page-header">
      <h2>{{ pageTitle }}</h2>
      <div class="header-actions" v-if="!isEditMode && couponsData.id">
        <el-button type="primary" icon="Edit" @click="handleEdit"
          >编辑</el-button
        >
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-if="isEditMode" class="edit-form">
      <el-form
        ref="couponsRef"
        :model="form"
        :rules="rules"
        label-width="160px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优惠券名称" prop="couponsName">
              <el-input
                v-model="form.couponsName"
                placeholder="请输入优惠券名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发行数量" prop="numberIssuances">
              <el-input-number
                v-model="form.numberIssuances"
                :min="1"
                :max="999999"
                placeholder="请输入发行数量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠券面值（元）" prop="faceValue">
              <el-input-number
                v-model="form.faceValue"
                :min="0"
                :precision="2"
                placeholder="请输入面值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="最低累计分享成功次数"
              prop="couponsShareMinimum"
            >
              <el-input-number
                v-model="form.couponsShareMinimum"
                :min="0"
                :max="999"
                placeholder="请输入最低分享次数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单满" prop="couponsCondition">
              <div class="flex w-full">
                <el-input-number
                  v-model="form.couponsCondition"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                  placeholder="请输入使用条件"
                />
                <el-form-item label="元可使用" label-width="75px">
                </el-form-item>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠券发行状态" prop="couponsState">
              <el-select
                v-model="form.couponsState"
                placeholder="请选择发行状态"
              >
                <el-option
                  v-for="item in coupons_state_dict"
                  :key="parseInt(item.value)"
                  :label="item.label"
                  :value="parseInt(item.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠券使用时间" prop="timeRange">
              <el-date-picker
                v-model="timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                @change="handleTimeRangeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="form-actions">
        <el-button type="primary" :loading="submitLoading" @click="submitForm"
          >保存</el-button
        >
        <el-button @click="cancelEdit">取消</el-button>
      </div>
    </div>

    <!-- 查看模式 -->
    <div v-else class="coupons-detail" v-loading="loading">
      <div class="detail-header">
        <h2>{{ couponsData.couponsName }}</h2>
        <div class="detail-meta">
          <span>优惠券编号：{{ couponsData.couponsId }}</span>
          <span>发行数量：{{ couponsData.numberIssuances }}张</span>
          <span>领取数量：{{ couponsData.numberReceive }}张</span>
          <span>面值：{{ couponsData.faceValue }}元</span>
          <span class="flex align-center"
            >发行状态：
            <dict-tag
              :options="coupons_state_dict"
              :value="couponsData.couponsState"
            />
          </span>
          <span>发行时间：{{ parseTime(couponsData.createTime) }}</span>
        </div>
      </div>

      <div class="detail-content">
        <div class="content-item">
          <label>使用条件：</label>
          <span>订单满{{ couponsData.couponsCondition }}元可使用</span>
        </div>
        <div class="content-item">
          <label>最低分享次数：</label>
          <span>{{ couponsData.couponsShareMinimum }}次</span>
        </div>
        <div class="content-item">
          <label>使用开始时间：</label>
          <span>{{ parseTime(couponsData.beginTime) }}</span>
        </div>
        <div class="content-item">
          <label>使用到期时间：</label>
          <span>{{ parseTime(couponsData.endTime) }}</span>
        </div>
      </div>

      <!-- 优惠券使用记录表格 -->
      <div class="usage-table" v-if="usageList && usageList.length > 0">
        <h3>使用记录</h3>
        <el-table
          :data="usageList"
          style="width: 100%"
          v-loading="usageLoading"
        >
          <el-table-column label="用户账号" align="center" prop="userAccount" />
          <el-table-column label="订单号" align="center" prop="orderNo" />
          <el-table-column
            label="领取时间"
            align="center"
            prop="collectionTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.collectionTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="使用时间"
            align="center"
            prop="usageTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.usageTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="分享次数"
            align="center"
            prop="couponsShare"
          />
          <el-table-column label="使用状态" align="center" prop="usageState">
            <template #default="scope">
              <dict-tag
                :options="usage_state_dict"
                :value="scope.row.usageState"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="no-data" v-else-if="!usageLoading">
        <el-empty description="暂无使用记录" />
      </div>
    </div>
  </div>
</template>

<script setup name="CouponsDetails">
import { ref, reactive, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  getCoupons,
  addCoupons,
  updateCoupons,
  getCouponsUsageList,
} from '@/api/operation/coupons';

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const loading = ref(true);
const submitLoading = ref(false);
const isEditMode = ref(false);
const couponsData = ref({});
const usageList = ref([]);
const usageLoading = ref(false);

// 字典数据
const { coupons_state_dict, usage_state_dict } = proxy.useDict(
  'coupons_state_dict',
  'usage_state_dict'
);

// 表单数据
const form = reactive({
  id: undefined,
  couponsId: undefined,
  couponsName: undefined,
  beginTime: undefined,
  endTime: undefined,
  numberIssuances: undefined,
  numberReceive: 0,
  couponsState: '1',
  createTime: undefined,
  faceValue: undefined,
  couponsCondition: undefined,
  couponsShareMinimum: 0,
});

// 表单验证规则
const rules = {
  couponsName: [
    { required: true, message: '优惠券名称不能为空', trigger: 'blur' },
  ],
  numberIssuances: [
    { required: true, message: '发行数量不能为空', trigger: 'blur' },
  ],
  faceValue: [
    { required: true, message: '优惠券面值不能为空', trigger: 'blur' },
  ],
  couponsCondition: [
    { required: true, message: '使用条件不能为空', trigger: 'blur' },
  ],
  beginTime: [
    { required: true, message: '使用开始时间不能为空', trigger: 'change' },
  ],
  endTime: [
    { required: true, message: '使用到期时间不能为空', trigger: 'change' },
  ],
  couponsState: [
    { required: true, message: '发行状态不能为空', trigger: 'change' },
  ],
};

// 计算页面标题
const pageTitle = computed(() => {
  if (isEditMode.value) {
    return couponsData.value.id ? '编辑优惠券' : '新增优惠券';
  }
  return '优惠券详情';
});

const timeRange = ref([]);

// 处理时间范围变化
function handleTimeRangeChange(value) {
  if (value && value.length === 2) {
    form.beginTime = value[0];
    form.endTime = value[1];
  } else {
    form.beginTime = undefined;
    form.endTime = undefined;
  }
}

// 获取优惠券使用记录
function getUsageList(couponsId) {
  if (!couponsId) return;

  usageLoading.value = true;
  getCouponsUsageList(couponsId)
    .then((response) => {
      usageList.value = response.data || response.rows || [];
    })
    .catch((error) => {
      console.error('获取使用记录失败:', error);
      usageList.value = [];
    })
    .finally(() => {
      usageLoading.value = false;
    });
}

onMounted(() => {
  const couponsId = route.params.id;
  const mode = route.query.mode; // 新增模式：add，编辑模式：edit

  if (mode === 'add') {
    // 新增模式
    isEditMode.value = true;
    resetForm();
  } else if (couponsId) {
    // 查看或编辑模式
    getCouponsDetail(couponsId);
    if (mode === 'edit') {
      isEditMode.value = true;
    }
  } else {
    proxy.$modal.msgError('缺少优惠券ID参数');
    goBack();
  }
});

function getCouponsDetail(couponsId) {
  loading.value = true;
  getCoupons(couponsId)
    .then((response) => {
      couponsData.value = response.data;
      // 如果是编辑模式，将数据填充到表单
      if (isEditMode.value) {
        Object.assign(form, response.data);
      }
      // 获取使用记录
      getUsageList(couponsId);
    })
    .catch((error) => {
      goBack();
    })
    .finally(() => {
      loading.value = false;
    });
}

function resetForm() {
  Object.assign(form, {
    id: undefined,
    couponsId: undefined,
    couponsName: undefined,
    beginTime: undefined,
    endTime: undefined,
    numberIssuances: undefined,
    numberReceive: 0,
    couponsState: '1',
    createTime: undefined,
    faceValue: undefined,
    couponsCondition: undefined,
    couponsShareMinimum: 0,
  });
  proxy.resetForm('couponsRef');
}

function handleEdit() {
  isEditMode.value = true;
  Object.assign(form, couponsData.value);
}

function cancelEdit() {
  if (couponsData.value.id) {
    // 如果是编辑现有优惠券，返回查看模式
    isEditMode.value = false;
  } else {
    // 如果是新增，返回列表页
    goBack();
  }
}

function submitForm() {
  proxy.$refs['couponsRef'].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      const api = form.id ? updateCoupons : addCoupons;
      api(form)
        .then((response) => {
          proxy.$modal.msgSuccess(form.id ? '修改成功' : '新增成功');
          if (form.id) {
            // 编辑成功，返回查看模式
            isEditMode.value = false;
            // 重新获取数据
            getCouponsDetail(form.couponsId || form.id);
          } else {
            // 新增成功，返回列表页
            goBack();
          }
        })
        .catch((error) => {
          proxy.$modal.msgError('操作失败');
          console.error('操作失败:', error);
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}

function goBack() {
  router.go(-1);
}
</script>

<style scoped>
.page-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  flex: 1;
}

.header-actions {
  margin-left: auto;
}

.edit-form {
  background: #fff;
  padding: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-actions {
  margin-top: 20px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.coupons-detail {
  padding: 20px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.detail-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
}

.detail-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  color: #909399;
  font-size: 14px;
}

.detail-content {
  line-height: 1.8;
  margin-bottom: 20px;
}

.content-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.content-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
  min-width: 120px;
}

.usage-table {
  margin-top: 20px;
}

.usage-table h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.no-data {
  margin-top: 20px;
  text-align: center;
}
</style>
