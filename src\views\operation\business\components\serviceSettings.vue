<template>
  <div>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="12">
        <el-card class="cardCalss">
          <template #header>
            <div class="cardHeader">
              <div class="cardHeaderLeft">
                <div class="block"></div>
                <div>旧回收价格设置</div>
              </div>
              <el-button
                type="primary"
                @click="save('agreementForm', 1)"
                :loading="loading.recycle"
                >保存</el-button
              >
            </div>
          </template>
          <el-form :model="formData" :rules="rules" ref="agreementForm">
            <el-table :data="formData.tableData" style="width: 100%">
              <el-table-column prop="numberNo" label="编码" align="center">
                <template #default="scope">
                  <el-form-item
                    label=""
                    :prop="'tableData.' + scope.$index + '.numberNo'"
                    :rules="rules.coding"
                  >
                    <el-input
                      v-model="scope.row.numberNo"
                      placeholder="请输入编码"
                      autocomplete="off"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                label="旧物回收"
                align="center"
                width="200"
              >
                <template #default="scope">
                  <el-form-item
                    label=""
                    :prop="'tableData.' + scope.$index + '.name'"
                    :rules="rules.oldThing"
                  >
                    <el-input
                      v-model="scope.row.name"
                      placeholder="请输入旧物回收"
                      autocomplete="off"
                    ></el-input>
                  </el-form-item> </template
              ></el-table-column>
              <el-table-column
                prop="price"
                label="单价(元/公斤)"
                align="center"
              >
                <template #default="scope">
                  <el-form-item
                    label=""
                    :prop="'tableData.' + scope.$index + '.price'"
                    :rules="rules.price"
                  >
                    <el-input
                      v-model="scope.row.price"
                      placeholder="请输入单价(元/公斤)"
                      autocomplete="off"
                      type="number"
                    ></el-input>
                  </el-form-item> </template
              ></el-table-column>
              <el-table-column label="操作" align="center">
                <template #default="scope">
                  <el-button
                    type="danger"
                    size="mini"
                    @click="deleteRow(scope.$index, 1)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
          <el-button style="margin-top: 20px" @click="addRow(1)" type="primary"
            >添加行</el-button
          >
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="12">
        <el-card class="cardCalss" style="min-height: 150px">
          <template #header>
            <div class="cardHeader">
              <div class="cardHeaderLeft">
                <div class="block"></div>
                <div>上门维修设置</div>
              </div>
              <el-button
                type="primary"
                @click="save('visitForm', 2)"
                :loading="loading.maintain"
                >保存</el-button
              >
            </div></template
          >
          <el-form :model="formData" :rules="rules" ref="visitForm">
            <el-form-item
              label="维修上门费"
              prop="maintainPrice"
              :rules="[
                {
                  required: true,
                  message: '请输入维修上门费',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="formData.maintainPrice"
                placeholder="请输入维修上门费"
                type="number"
                autocomplete="off"
              >
                <template #append>(元/次)</template></el-input
              >
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="cardCalss" style="min-height: 150px">
          <template #header>
            <div class="cardHeader">
              <div class="cardHeaderLeft">
                <div class="block"></div>
                <div>非会员钱包余额冻结</div>
              </div>
              <el-button
                type="primary"
                @click="save('frostForm', 4)"
                :loading="loading.frost"
                >保存</el-button
              >
            </div></template
          >
          <el-form :model="formData" :rules="rules" ref="frostForm">
            <el-form-item
              label="余额冻结"
              prop="frostPrice"
              :rules="[
                {
                  required: true,
                  message: '请输入余额冻结金额',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="formData.frostPrice"
                placeholder="请输入余额冻结金额"
                type="number"
                autocomplete="off"
              >
                <template #append>(元)</template></el-input
              >
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="cardCalss">
          <template #header>
            <div class="cardHeader">
              <div class="cardHeaderLeft">
                <div class="block"></div>
                <div>快递代取价格设置</div>
              </div>
              <el-button
                type="primary"
                @click="save('expressageForm', 3)"
                :loading="loading.expressage"
                >保存</el-button
              >
            </div></template
          >
          <el-form :model="formData" :rules="rules" ref="expressageForm">
            <el-table :data="formData.expressageData" style="width: 100%">
              <el-table-column prop="numberNo" label="编码" align="center">
                <template #default="scope">
                  <el-form-item
                    label=""
                    :prop="'expressageData.' + scope.$index + '.numberNo'"
                    :rules="rules.coding"
                  >
                    <el-input
                      v-model="scope.row.numberNo"
                      placeholder="请输入编码"
                      autocomplete="off"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                label="快递量级"
                align="center"
                width="200"
              >
                <template #default="scope">
                  <el-form-item
                    label=""
                    :prop="'expressageData.' + scope.$index + '.name'"
                    :rules="rules.expressage"
                  >
                    <el-input
                      v-model="scope.row.name"
                      placeholder="请输入快递量级"
                      autocomplete="off"
                    ></el-input>
                  </el-form-item> </template
              ></el-table-column>
              <el-table-column
                prop="price"
                label="单价(元/公斤)"
                align="center"
              >
                <template #default="scope">
                  <el-form-item
                    label=""
                    :prop="'expressageData.' + scope.$index + '.price'"
                    :rules="rules.price"
                  >
                    <el-input
                      v-model="scope.row.price"
                      placeholder="请输入单价(元/公斤)"
                      autocomplete="off"
                      type="number"
                    ></el-input>
                  </el-form-item> </template
              ></el-table-column>
              <el-table-column label="操作" align="center">
                <template #default="scope">
                  <el-button
                    type="danger"
                    size="mini"
                    @click="deleteRow(scope.$index, 3)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
          <el-button style="margin-top: 20px" @click="addRow(3)" type="primary"
            >添加行</el-button
          >
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="ServiceSettings">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { getSettingByKey, addOrUpdateSetting } from '@/api/operation/business';

const { proxy } = getCurrentInstance();

// 响应式数据
const loading = ref({
  recycle: false,
  maintain: false,
  frost: false,
  expressage: false,
});

const rules = reactive({
  coding: [{ required: true, message: '请输入编码', trigger: 'blur' }],
  price: [{ required: true, message: '请输入单价(元/公斤)', trigger: 'blur' }],
  oldThing: [{ required: true, message: '请输入旧物回收', trigger: 'blur' }],
  expressage: [{ required: true, message: '请输入快递量级', trigger: 'blur' }],
});

const formData = reactive({
  maintainPrice: '', // 上门维修费
  frostPrice: '', // 余额冻结金额
  tableData: [
    {
      numberNo: '',
      name: '',
      price: '',
    },
  ],
  expressageData: [
    {
      numberNo: '',
      name: '',
      price: '',
    },
  ],
});

// 生命周期
onMounted(() => {
  getSettingsData();
});

// 方法
/** 获取所有设置数据 */
async function getSettingsData() {
  try {
    // 并行获取四个设置数据
    const [recycleRes, maintainRes, frostRes, expressageRes] =
      await Promise.all([
        getSettingByKey('recycle'),
        getSettingByKey('maintain'),
        getSettingByKey('frost'),
        getSettingByKey('expressage'),
      ]);

    // 处理旧回收价格设置数据
    if (recycleRes.data) {
      const recycleData = recycleRes.data;
      formData.tableData =
        recycleData.length > 0
          ? recycleData
          : [
              {
                numberNo: '',
                name: '',
                price: '',
              },
            ];
    }

    // 处理上门维修设置数据
    if (maintainRes.data) {
      const maintainData = maintainRes.data;
      formData.maintainPrice =
        maintainData.length > 0 ? maintainData[0].price : '';
    }

    // 处理非会员钱包余额冻结数据
    if (frostRes.data) {
      const frostData = frostRes.data;
      formData.frostPrice = frostData.length > 0 ? frostData[0].price : '';
    }

    // 处理快递代取价格设置数据
    if (expressageRes.data) {
      const expressageData = expressageRes.data;
      formData.expressageData =
        expressageData.length > 0
          ? expressageData
          : [
              {
                numberNo: '',
                name: '',
                price: '',
              },
            ];
    }
  } catch (error) {
    console.error('获取设置数据失败:', error);
    proxy.$modal.msgError('获取设置数据失败');
  }
}

/** 保存按钮 */
async function save(refValue, type) {
  const formRef = proxy.$refs[refValue];
  if (!formRef) return;
  formRef.validate(async (valid) => {
    if (valid) {
      let key = '';
      let value = '';
      let loadingKey = '';
      try {
        switch (type) {
          case 1: // 旧回收价格设置
            key = 'recycle';
            value = formData.tableData;
            loadingKey = 'recycle';
            break;
          case 2: // 上门维修设置
            key = 'maintain';
            value = [{ price: formData.maintainPrice }];
            loadingKey = 'maintain';
            break;
          case 3: // 快递代取价格设置
            key = 'expressage';
            value = formData.expressageData;
            loadingKey = 'expressage';
            break;
          case 4: // 非会员钱包余额冻结
            key = 'frost';
            value = [{ price: formData.frostPrice }];
            loadingKey = 'frost';
            break;
        }
        loading.value[loadingKey] = true;
        const data = {
          key,
          value,
        };
        await addOrUpdateSetting(data);
        proxy.$modal.msgSuccess('保存成功');
      } catch (error) {
        console.error('保存失败:', error);
        proxy.$modal.msgError('保存失败');
      } finally {
        loading.value[loadingKey] = false;
      }
    } else {
      return false;
    }
  });
}

/** 添加行 */
function addRow(type) {
  if (type == 1) {
    formData.tableData.push({
      numberNo: '',
      name: '',
      price: '',
    });
  } else if (type == 3) {
    formData.expressageData.push({
      numberNo: '',
      name: '',
      price: '',
    });
  }
}

/** 删除行 */
function deleteRow(index, type) {
  if (type == 1) {
    if (formData.tableData.length > 1) {
      formData.tableData.splice(index, 1);
    } else {
      proxy.$modal.msgWarning('至少保留一行数据');
    }
  } else if (type == 3) {
    if (formData.expressageData.length > 1) {
      formData.expressageData.splice(index, 1);
    } else {
      proxy.$modal.msgWarning('至少保留一行数据');
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 10px 15px;
  background: #eeeeee;
}
</style>
