import request from '@/utils/request';

// 组织用户列表
export function listOrganizationalUser(params) {
  return request({
    url: '/platform/user/list',
    method: 'post',
    data: params,
  });
}
// 用户详情
export function getOrganizationalUserDetail(userId) {
  return request({
    url: `/platform/user/${userId}`,
    method: 'post',
  });
}
// 获取角色列表
export function getOrganizationalRoles() {
  return request({
    url: '/platform/platform/role/index',
    method: 'post',
  });
}

// 更新用户角色
export function updateOrganizationalUserRole(data) {
  return request({
    url: '/platform/user/updateUserRole',
    method: 'post',
    data,
  });
}

// 审核用户
export function auditUser(data) {
  return request({
    url: '/platform/user/userAuthAudit',
    method: 'post',
    data,
  });
}

// 根据级别查询网点或者驿站
export function getLevelUser(level) {
  return request({
    url: `/platform/user/getLevel/${level}`,
    method: 'get',
  });
}
