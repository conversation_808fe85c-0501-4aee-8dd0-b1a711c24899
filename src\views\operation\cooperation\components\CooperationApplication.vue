<template>
  <div class="cooperation-application">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="订单编号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="到期时间" prop="expireTime">
        <el-date-picker
          v-model="queryParams.expireTime"
          type="datetime"
          placeholder="请选择到期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="合作商名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入合作商名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入昵称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="applicationList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="手机号" align="center" prop="mobile" />
      <el-table-column
        label="情况说明"
        align="center"
        prop="remark"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="提交时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="state">
        <template #default="scope">
          <dict-tag :options="process_status" :value="scope.row.state" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleProcess(scope.row)"
            :disabled="scope.row.state === 1"
            >处理</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="CooperationApplication">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import {
  listCooperationApplication,
  processEnterApplication,
} from '@/api/operation/cooperation';

const { proxy } = getCurrentInstance();

const applicationList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const total = ref(0);

// 字典数据
const { process_status } = proxy.useDict('process_status');

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    orderNo: undefined,
    expireTime: undefined,
    name: undefined,
    mobile: undefined,
    nickName: undefined,
    websiteName: undefined,
    stationName: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;

  listCooperationApplication(queryParams.value)
    .then((response) => {
      applicationList.value = response.data?.records || [{}];
      total.value = response.data?.total || 0;
    })

    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
}

function handleProcess(row) {
  // 如果已经处理过，不允许再次处理
  if (row.state === 1) {
    proxy.$modal.msgWarning('该申请已处理，无法重复处理');
    return;
  }

  proxy.$modal
    .confirm('确认要处理该入驻申请吗？', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      processApplication(row.id);
    });
}

function processApplication(id) {
  const loading = proxy.$loading({
    lock: true,
    text: '处理中...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  processEnterApplication(id)
    .then((response) => {
      proxy.$modal.msgSuccess('处理成功');
      getList(); // 刷新列表
    })

    .finally(() => {
      loading.close();
    });
}
</script>

<style scoped>
.cooperation-application {
  padding: 20px 0;
}
</style>
