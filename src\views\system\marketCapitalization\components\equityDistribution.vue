﻿<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="equityList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账号" align="center" prop="mobile" />
      <el-table-column label="昵称" align="center" prop="nickname" />
      <el-table-column label="手机" align="center" prop="mobile" />
      <el-table-column label="绑定名称" align="center" prop="bindName" />
      <el-table-column
        label="地址"
        align="center"
        prop="address"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="级别" align="center" prop="type">
        <template #default="scope">
          <dict-tag :options="user_level" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column
        label="持有数量（股）"
        align="center"
        prop="allocationStock"
      />
      <el-table-column
        label="注册时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleEquityDistribution(scope.row)"
            >股权分配</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 股权分配对话框 -->
    <el-dialog
      title="股权分配"
      v-model="equityOpen"
      width="800px"
      append-to-body
    >
      <!-- 用户基础信息 -->
      <div class="user-info-section">
        <h4>基础信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">账号：</span>
              <span class="value">{{ equityForm.mobile }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">昵称：</span>
              <span class="value">{{ equityForm.nickname }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">手机号：</span>
              <span class="value">{{ equityForm.mobile }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">级别：</span>
              <span class="value">
                <dict-tag :options="user_level" :value="equityForm.type" />
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">代理商：</span>
              <span class="value">{{ equityForm.numberNo || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">网点：</span>
              <span class="value">{{ equityForm.bindName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">驿站：</span>
              <span class="value">{{ equityForm.address || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 股权操作区域 -->
      <div class="equity-operation-section">
        <h4>股权操作</h4>
        <div class="current-hold">
          <span class="label">所持股份：</span>
          <span class="value">{{ equityForm.currentHold }}股</span>
        </div>
        <div class="add-equity">
          <span class="label">此处新增</span>
          <el-input
            v-model="equityForm.changeNumber"
            type="number"
            placeholder="请输入变动数量"
            class="w-[200px] mx-2"
          />
          <span class="label">股</span>
          <el-button
            type="primary"
            :loading="equitySubmitLoading"
            @click="submitEquityForm"
            style="margin-left: 10px"
          >
            新增
          </el-button>
        </div>
      </div>

      <!-- 股权变动记录表格 -->
      <div class="equity-history-section">
        <h4>股权变动记录</h4>
        <el-table
          v-loading="historyLoading"
          :data="equityHistory"
          style="width: 100%"
        >
          <el-table-column
            label="新增时间"
            align="center"
            prop="createTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="变动数量（股）"
            align="center"
            prop="changeNumber"
          >
            <template #default="scope">
              <span
                :class="scope.row.changeNumber > 0 ? 'positive' : 'negative'"
              >
                {{ scope.row.changeNumber > 0 ? '+' : ''
                }}{{ scope.row.changeNumber }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="持有数量" align="center" prop="numberStock" />
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer is-center">
          <el-button @click="cancelEquity">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EquityDistribution">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import {
  listEquityUsers,
  getEquityHistory,
  addEquityChange,
} from '@/api/system/marketCapitalization/equityDistribution';

const { proxy } = getCurrentInstance();

const equityList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const total = ref(0);
const equityOpen = ref(false);
const equitySubmitLoading = ref(false);
const historyLoading = ref(false);
const equityHistory = ref([]);

// 字典数据
const { user_level } = proxy.useDict('user_level');

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    mobile: undefined,
  },
  equityForm: {
    userId: undefined,
    mobile: '',
    nickname: '',
    mobile: '',
    type: undefined,
    numberNo: '',
    bindName: '',
    address: '',
    currentHold: 0,
    changeNumber: 0,
  },
  equityRules: {
    changeNumber: [
      { required: true, message: '变动数量不能为空', trigger: 'blur' },
    ],
  },
});

const { queryParams, equityForm, equityRules } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  listEquityUsers(queryParams.value)
    .then((response) => {
      equityList.value = response.data?.records;
      total.value = response.data?.total || 0;
    })
    .catch((error) => {
      proxy.$modal.msgError('获取用户列表失败');
      console.error('获取用户列表失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
}

function handleEquityDistribution(row) {
  equityForm.value.userId = row.userId;
  equityForm.value.mobile = row.mobile;
  equityForm.value.nickname = row.nickname;
  equityForm.value.mobile = row.mobile;
  equityForm.value.type = row.type;
  equityForm.value.numberNo = row.numberNo;
  equityForm.value.bindName = row.bindName;
  equityForm.value.address = row.address;
  equityForm.value.currentHold = row.allocationStock || 0;
  equityForm.value.changeNumber = 0;

  equityOpen.value = true;

  // 获取股权变动记录
  getEquityHistoryList(row.userId);
}

function getEquityHistoryList(userId) {
  historyLoading.value = true;
  getEquityHistory(userId)
    .then((response) => {
      equityHistory.value = response.data || [];
    })

    .finally(() => {
      historyLoading.value = false;
    });
}

function cancelEquity() {
  equityOpen.value = false;
  equityForm.value.userId = undefined;
  equityForm.value.mobile = '';
  equityForm.value.nickname = '';
  equityForm.value.mobile = '';
  equityForm.value.type = undefined;
  equityForm.value.numberNo = '';
  equityForm.value.bindName = '';
  equityForm.value.address = '';
  equityForm.value.currentHold = 0;
  equityForm.value.changeNumber = 0;
  equityHistory.value = [];
}

function submitEquityForm() {
  if (!equityForm.value.changeNumber) {
    proxy.$modal.msgWarning('请输入变动数量');
    return;
  }

  equitySubmitLoading.value = true;
  const data = {
    userId: equityForm.value.userId,
    changeNumber: equityForm.value.changeNumber,
  };

  addEquityChange(data)
    .then((response) => {
      proxy.$modal.msgSuccess('股权变动成功');
      // 更新当前持有数量
      equityForm.value.currentHold += Number(equityForm.value.changeNumber);
      equityForm.value.changeNumber = 0;
      // 刷新股权变动记录
      getEquityHistoryList(equityForm.value.userId);
      // 刷新主列表
      getList();
    })

    .finally(() => {
      equitySubmitLoading.value = false;
    });
}
</script>

<style scoped>
.user-info-section,
.equity-operation-section,
.equity-history-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
}

.user-info-section h4,
.equity-operation-section h4,
.equity-history-section h4 {
  margin: 0 0 16px 0;
  font-weight: 600;
  color: #303133;
}

.info-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.info-item .label {
  color: #606266;
  font-weight: 500;
}

.info-item .value {
  color: #303133;
}

.current-hold {
  margin-bottom: 16px;
  font-size: 16px;
}

.current-hold .label {
  color: #606266;
  font-weight: 500;
}

.current-hold .value {
  color: #409eff;
  font-weight: 600;
}

.add-equity {
  display: flex;
  align-items: center;
}

.add-equity .label {
  color: #606266;
  font-weight: 500;
}

.positive {
  color: #67c23a;
  font-weight: 600;
}

.negative {
  color: #f56c6c;
  font-weight: 600;
}

.dialog-footer.is-center {
  display: flex;
  justify-content: center;
}
</style>
