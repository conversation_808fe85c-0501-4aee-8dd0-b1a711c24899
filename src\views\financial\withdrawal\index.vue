<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="打款状态" prop="paymentState">
        <el-select
          v-model="queryParams.paymentState"
          placeholder="请选择打款状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in payment_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="级别" prop="level">
        <el-select
          v-model="queryParams.level"
          placeholder="请选择级别"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in mobile_user_level"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请单类别" prop="applyClass">
        <el-select
          v-model="queryParams.applyClass"
          placeholder="请选择申请单类别"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in application_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>
      <el-form-item label="手机" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="帐号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入帐号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="withdrawalList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="帐号" align="center" prop="mobile" width="120" />
      <el-table-column
        label="手机号"
        align="center"
        prop="mobile"
        width="120"
      />
      <el-table-column label="级别" align="center" prop="level" width="100">
        <template #default="scope">
          <dict-tag :options="mobile_user_level" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column
        label="申请单类别"
        align="center"
        prop="applyClass"
        width="120"
      >
        <template #default="scope">
          <dict-tag :options="application_type" :value="scope.row.applyClass" />
        </template>
      </el-table-column>
      <el-table-column label="网点名称" align="center" prop="websiteName" />
      <el-table-column label="驿站名称" align="center" prop="stationName" />
      <el-table-column
        label="提现金额(元)"
        align="center"
        prop="withdrawalAmount"
        width="120"
      >
        <template #default="scope">
          <span>￥{{ scope.row.withdrawalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="提现申请时间"
        align="center"
        prop="applyTime"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.applyTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="打款方式"
        align="center"
        prop="paymentWay"
        width="100"
      />
      <el-table-column
        label="打款信息"
        align="center"
        prop="paymentInfo"
        width="200"
      >
        <template #default="scope">
          <div class="payment-info">
            <div>帐户:{{ scope.row.paymentAccount }}</div>
            <div>账号:{{ scope.row.paymentAccountNo }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="打款状态"
        align="center"
        prop="paymentState"
        width="100"
      >
        <template #default="scope">
          <dict-tag :options="payment_status" :value="scope.row.paymentState" />
        </template>
      </el-table-column>
      <el-table-column
        label="打款时间"
        align="center"
        prop="paymentTime"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.paymentTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="100"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-if="scope.row.paymentState === 0"
            link
            type="primary"
            @click="handlePayment(scope.row)"
          >
            打款
          </el-button>
          <el-button v-else link type="info" disabled> 打款 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部统计信息 -->
    <div class="summary-info">
      累计提现笔数: {{ totalCount }}笔 累计提现金额: ￥{{ totalAmount }}元
    </div>
  </div>
</template>

<script setup name="Withdrawal">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { getWithdrawalList, processWithdrawal } from '@/api/financial';

const { proxy } = getCurrentInstance();

const withdrawalList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);
const totalCount = ref();
const totalAmount = ref();
const dateRange = ref([]);

// 字典数据
const {
  payment_status,
  mobile_user_level,
  application_type,
  outlet_name_dict,
  station_name_dict,
} = proxy.useDict(
  'payment_status',
  'mobile_user_level',
  'application_type',
  'outlet_name_dict',
  'station_name_dict'
);

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    startTime: undefined,
    endTime: undefined,
    paymentState: undefined,
    level: undefined,
    applyClass: undefined,
    websiteName: undefined,
    stationName: undefined,
    mobile: undefined,
    mobile: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  getWithdrawalList(queryParams.value)
    .then((response) => {
      withdrawalList.value = response.data?.page?.records || [];
      total.value = response.data?.page?.total || 0;
      totalCount.value = response.data?.totalRecords || 0;
      totalAmount.value = response.data?.totalAmount || 0;
    })

    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  dateRange.value = [];
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleDateChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.value.startTime = dates[0];
    queryParams.value.endTime = dates[1];
  } else {
    queryParams.value.startTime = undefined;
    queryParams.value.endTime = undefined;
  }
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}

function handlePayment(row) {
  proxy.$modal
    .confirm('确认要打款给该用户吗？')
    .then(function () {
      return processWithdrawal(row.id);
    })
    .then((response) => {
      getList();
      proxy.$modal.msgSuccess('打款成功');
    })
    .catch((error) => {
      proxy.$modal.msgError('打款失败');
      console.error('打款失败:', error);
    });
}
</script>

<style scoped>
.payment-info {
  font-size: 12px;
  line-height: 1.4;
  text-align: left;
}

.summary-info {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
</style>
