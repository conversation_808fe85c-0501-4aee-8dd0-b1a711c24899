<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
          @change="handleDateChange"
        />
      </el-form-item>

      <el-form-item label="手机" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="annualFeeList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="帐号" align="center" prop="mobile" width="120" />
      <el-table-column
        label="手机号"
        align="center"
        prop="mobile"
        width="120"
      />
      <el-table-column label="合作商绑定名称" align="center" prop="bindName" />
      <el-table-column
        label="订单编号"
        align="center"
        prop="orderNo"
        width="150"
      />
      <el-table-column label="续费金额" align="center" prop="price" width="120">
        <template #default="scope">
          <span>￥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="订单时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="100"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部统计信息 -->
    <div class="summary-info">
      累计订单笔数: {{ totalCount }}笔 累计金额: ￥{{ totalAmount }}元
    </div>
  </div>
</template>

<script setup name="PartnerAnnualFee">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { getPartnerAnnualFeeList } from '@/api/financial';

const { proxy } = getCurrentInstance();

const router = useRouter();

const annualFeeList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);
const totalCount = ref();
const totalAmount = ref();
const dateRange = ref([]);

// 详情弹窗相关
const detailDialogVisible = ref(false);
const detailInfo = ref({});

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    startTime: undefined,
    endTime: undefined,
    mobile: undefined,
    nickName: undefined,
    websiteId: undefined,
    stationId: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  getPartnerAnnualFeeList(queryParams.value)
    .then((response) => {
      annualFeeList.value = response.data?.page?.records || [];
      total.value = response.data?.page?.total || 0;
      totalAmount.value = response.data?.allPrice;
      totalCount.value = response.data?.partnerCount;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  dateRange.value = [];
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleDateChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.value.startTime = dates[0];
    queryParams.value.endTime = dates[1];
  } else {
    queryParams.value.startTime = undefined;
    queryParams.value.endTime = undefined;
  }
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}

function handleDetail(row) {
  router.push({
    path: '/financial/partnerAnnualFee/details/' + row.userId,
  });
}
</script>

<style scoped>
.summary-info {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

/* 详情弹窗样式 */
.detail-content {
  padding: 20px 0;
}

.info-section {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
  padding-bottom: 8px;
  position: relative;
}

.section-title::after {
  content: '';
  width: 4px;
  height: 15px;
  background: #409eff;
  position: absolute;
  left: -8px;
  top: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.amount {
  color: #f56c6c;
  font-weight: 600;
}

.dialog-footer {
  text-align: center;
}
</style>
