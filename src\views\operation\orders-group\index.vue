<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="商城订单" name="mallOrders"> </el-tab-pane>
      <el-tab-pane label="已发团商品" name="groupProducts"> </el-tab-pane>
    </el-tabs>
    <MallOrders v-if="activeTab === 'mallOrders'" />
    <GroupProducts v-if="activeTab === 'groupProducts'" />
  </div>
</template>

<script setup name="OrdersGroup">
import { ref } from 'vue';
import MallOrders from './components/MallOrders.vue';
import GroupProducts from './components/GroupProducts.vue';

const activeTab = ref('mallOrders');

function handleTabClick(tab) {
  console.log('切换到标签页:', tab.props.name);
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
