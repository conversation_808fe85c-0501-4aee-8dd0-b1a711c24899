import request from '@/utils/request';

// 角色列表
export function listRole(params) {
  return request({
    url: '/platform/platform/role/index',
    method: 'post',
    data: params,
  });
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/platform/platform/role/add',
    method: 'post',
    data,
  });
}

// 编辑角色
export function updateRole(data) {
  return request({
    url: '/platform/platform/role/edit',
    method: 'post',
    data,
  });
}

// 删除角色
export function delRole(roleId) {
  return request({
    url: '/platform/platform/role/delete?roleId=' + roleId,
    method: 'post',
  });
}
