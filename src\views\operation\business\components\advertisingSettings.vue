<template>
  <div>
    <el-row :gutter="20">
      <el-col
        v-for="(item, index) in cardList"
        :key="index"
        :xs="24"
        :sm="8"
        :md="6"
      >
        <el-card class="cardCalss">
          <template #header>
            <div class="cardHeader">
              <div class="cardHeaderLeft">
                <div class="block"></div>
                <div>{{ item.title }}</div>
              </div>
              <el-button type="primary" @click="save(item, index)"
                >保存</el-button
              >
            </div></template
          >
          <ImageUpload
            v-model="item.url"
            size="100%"
            height="350px"
            :fileType="['jpg', 'png', 'jpeg', 'mp4']"
            :limit="1"
            :fileSize="50"
            :isShowTip="false"
          >
            <span>视频或图片</span>
          </ImageUpload>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="AdvertisingSettings">
import { ref, onMounted, getCurrentInstance } from 'vue';
import { advertisingList } from './data.js';
import {
  addAdvertising,
  editAdvertising,
  getAdvertisingList,
} from '@/api/operation/business';

const { proxy } = getCurrentInstance();

// 响应式数据
const cardList = ref(advertisingList);

// 生命周期
onMounted(() => {
  getAdvertisingListData();
});

// 方法
/** 保存按钮 */
async function save(item, index) {
  try {
    const data = {
      advertisingUrl: item.url,
      advertisingType: index + 1,
    };
    if (item.id) {
      data.id = item.id;
      await editAdvertising(data);
    } else {
      await addAdvertising(data);
    }
    proxy.$message.success('保存成功');
    getAdvertisingListData();
  } catch (error) {
    proxy.$message.error('保存失败');
  }
}

/** 获取广告列表 */
async function getAdvertisingListData() {
  try {
    const res = await getAdvertisingList();
    res.data.forEach((item) => {
      advertisingList[item.advertisingType - 1].url = item.advertisingUrl;
      advertisingList[item.advertisingType - 1].id = item.id;
    });
    cardList.value = [...advertisingList];
  } catch (error) {
    console.error('获取广告列表失败:', error);
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 10px 15px;
  background: #eeeeee;
}
</style>
