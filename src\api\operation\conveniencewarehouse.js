import request from '@/utils/request';

// 获取便民库列表
export function listConvenienceWarehouse(params) {
  return request({
    url: '/platform/conveniencewarehouse/page',
    method: 'post',
    data: params,
  });
}

// 获取便民库详情
export function getConvenienceWarehouse(id) {
  return request({
    url: `/platform/conveniencewarehouse/${id}`,
    method: 'get',
  });
}

// 新增便民库
export function addConvenienceWarehouse(data) {
  return request({
    url: '/platform/conveniencewarehouse/save',
    method: 'post',
    data,
  });
}

// 修改便民库
export function updateConvenienceWarehouse(data) {
  return request({
    url: '/platform/conveniencewarehouse/update',
    method: 'post',
    data,
  });
}

// 删除便民库
export function delConvenienceWarehouse(id) {
  return request({
    url: `/platform/conveniencewarehouse/${id}`,
    method: 'delete',
  });
}
