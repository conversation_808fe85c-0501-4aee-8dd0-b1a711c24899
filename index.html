<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/wl.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智慧社区生活服务综合运营管理系统</title>
    <style>
      html,
      body,
      #app {
        height: 100%;
        margin: 0px;
        padding: 0px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          Helvetica, Arial, sans-serif;
      }

      .loading-screen {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;
        overflow: hidden;
      }

      .background-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
      }

      .shape {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          rgba(0, 122, 255, 0.05) 0%,
          rgba(0, 122, 255, 0.02) 100%
        );
      }

      .shape-1 {
        width: 300px;
        height: 300px;
        top: -100px;
        right: -100px;
      }

      .shape-2 {
        width: 200px;
        height: 200px;
        bottom: 20%;
        left: -50px;
      }

      .shape-3 {
        width: 150px;
        height: 150px;
        bottom: 10%;
        right: 10%;
      }

      .loading-content {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: rgba(255, 255, 255, 0.8);
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(10px);
      }

      .loading-title {
        font-size: 24px;
        color: #1d1d1f;
        margin-top: 30px;
        font-weight: 500;
        letter-spacing: 0.5px;
      }

      .loading-container {
        position: relative;
        width: 200px;
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .loading-dots {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
      }

      .dot {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background-color: #ef6901;
        animation: bounce 1.4s infinite ease-in-out both;
      }

      .dot:nth-child(1) {
        animation-delay: -0.32s;
      }

      .dot:nth-child(2) {
        animation-delay: -0.16s;
      }

      @keyframes bounce {
        0%,
        80%,
        100% {
          transform: scale(0);
        }
        40% {
          transform: scale(1);
        }
      }

      .loading-logo {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 32px;
        font-weight: 600;
        background: linear-gradient(135deg, #1d1d1f 0%, #434343 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 60px;
      }

      .loading-progress {
        width: 200px;
        height: 4px;
        background-color: #e5e5e5;
        border-radius: 2px;

        overflow: hidden;
        position: relative;
      }

      .loading-bar {
        position: absolute;
        height: 100%;
        width: 30%;
        background: linear-gradient(90deg, #ef6901, #ff994a);
        border-radius: 2px;
        animation: progress 1.5s ease-in-out infinite;
      }

      @keyframes progress {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(400%);
        }
      }

      .loading-subtitle {
        font-size: 16px;
        color: #86868b;
        margin-top: 10px;
        text-align: center;
      }

      .loading-percentage {
        position: absolute;
        right: 0;
        top: -20px;
        font-size: 14px;
        color: #007aff;
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="loading-screen">
        <div class="background-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
        <div class="loading-content">
          <!-- <div class="loading-logo">VMS</div> -->
          <div class="loading-container">
            <div class="loading-dots">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </div>
          <div class="loading-progress">
            <div class="loading-bar"></div>
            <div class="loading-percentage">85%</div>
          </div>
          <div class="loading-title">正在加载系统资源</div>
          <div class="loading-subtitle">智慧社区生活服务综合运营管理系统</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
