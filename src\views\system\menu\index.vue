<template>
  <div class="app-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="平台菜单" name="pc">
        <Pc v-if="activeTab === 'pc'" />
      </el-tab-pane>
      <el-tab-pane label="移动端菜单" name="mobile">
        <Mobile v-if="activeTab === 'mobile'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import Pc from './components/Pc.vue';
import Mobile from './components/Mobile.vue';

const activeTab = ref('pc');
</script>
