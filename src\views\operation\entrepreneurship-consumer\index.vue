<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="会员卡状态" prop="cardStatus">
        <el-select
          v-model="queryParams.cardStatus"
          placeholder="请选择会员卡状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in card_status_dict"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="会员卡标识" prop="cardType">
        <el-select
          v-model="queryParams.cardType"
          placeholder="请选择会员卡标识"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in card_type_dict"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="创业者手机" prop="entrepreneurPhone">
        <el-input
          v-model="queryParams.entrepreneurPhone"
          placeholder="请输入创业者手机"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="店家手机" prop="storePhone">
        <el-input
          v-model="queryParams.storePhone"
          placeholder="请输入店家手机"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="会员手机" prop="memberPhone">
        <el-input
          v-model="queryParams.memberPhone"
          placeholder="请输入会员手机"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="consumerList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会员卡编号" align="center" prop="cardNumber" />
      <el-table-column label="会员卡名称" align="center" prop="cardName" />
      <el-table-column label="消费店铺名称" align="center" prop="storeName" />
      <el-table-column label="单次消费价格" align="center" prop="singlePrice">
        <template #default="scope">
          <span>¥{{ scope.row.singlePrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会员享受价格" align="center" prop="memberPrice">
        <template #default="scope">
          <span>¥{{ scope.row.memberPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="可消费次数"
        align="center"
        prop="consumptionTimes"
      />
      <el-table-column label="预存金额" align="center" prop="prepaidAmount">
        <template #default="scope">
          <span>¥{{ scope.row.prepaidAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishTime"
        width="180"
      >
        <template #default="scope">
          <span>{{
            parseTime(scope.row.publishTime, '{y}-{m}-{d} {h}:{i}:{s}')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="垫付金额" align="center" prop="advanceAmount">
        <template #default="scope">
          <span>¥{{ scope.row.advanceAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会员卡标识" align="center" prop="cardType">
        <template #default="scope">
          <dict-tag :options="card_type_dict" :value="scope.row.cardType" />
        </template>
      </el-table-column>
      <el-table-column label="会员卡状态" align="center" prop="cardStatus">
        <template #default="scope">
          <dict-tag :options="card_status_dict" :value="scope.row.cardStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="抢购时间"
        align="center"
        prop="purchaseTime"
        width="180"
      >
        <template #default="scope">
          <span>{{
            parseTime(scope.row.purchaseTime, '{y}-{m}-{d} {h}:{i}:{s}')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
            v-hasPermi="['operation:entrepreneurship:view']"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="EntrepreneurshipConsumer">
import { listEntrepreneurshipConsumer } from '@/api/operation/entrepreneurshipConsumer';

const { proxy } = getCurrentInstance();
const { card_status_dict, card_type_dict } = proxy.useDict(
  'card_status',
  'card_type'
);

const consumerList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const dateRange = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    publishTime: null,
    cardStatus: null,
    cardType: null,
    entrepreneurPhone: null,
    storePhone: null,
    memberPhone: null,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询创业消费联盟列表 */
function getList() {
  loading.value = true;
  queryParams.value.publishTime = dateRange.value;
  listEntrepreneurshipConsumer(queryParams.value)
    .then((response) => {
      consumerList.value = response.rows;
      total.value = response.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    cardNumber: null,
    cardName: null,
    storeName: null,
    singlePrice: null,
    memberPrice: null,
    consumptionTimes: null,
    prepaidAmount: null,
    publishTime: null,
    advanceAmount: null,
    cardType: null,
    cardStatus: null,
    purchaseTime: null,
  };
  proxy.resetForm('consumerRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 详情按钮操作 */
function handleView(row) {
  proxy.$modal.msgInfo('详情功能待开发');
}

onMounted(() => {
  getList();
});
</script>
