import request from '@/utils/request';

// 商城订单列表
export function listMallOrders(params) {
  return request({
    url: '/platform/order/page',
    method: 'post',
    data: params,
  });
}
// 商城订单详情
export function getMallOrderDetail(id) {
  return request({
    url: `/platform/order/${id}`,
    method: 'get',
  });
}

// 已发团商品列表
export function listGroupProducts(params) {
  return request({
    url: '/platform/product/page',
    method: 'post',
    data: params,
  });
}

// 已发团商品详情
export function getGroupProductDetail(productId) {
  return request({
    url: `/platform/product/detail/${productId}`,
    method: 'get',
  });
}
