﻿<template>
  <div class="app-container">
    <div class="page-header">
      <h2>合作商交纳年份费详情</h2>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 合作商信息 -->
      <div class="info-section">
        <div class="section-title">合作商信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>昵称：</label>
              <span>{{ detail.nickname || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>手机：</label>
              <span>{{ detail.mobile || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>绑定名称：</label>
              <span>{{ detail.bindName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>地址：</label>
              <span>{{ detail.address || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>负责人：</label>
              <span>{{ detail.bindBy || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>负责人手机：</label>
              <span>{{ detail.bindByPhone || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>绑定驿站：</label>
              <span>{{ detail.stationName || '--' }}</span>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="info-item">
              <label>帐号：</label>
              <span>{{ detail.mobile || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>主副类型：</label>
              <dict-tag :options="yes_or_no" :value="detail.isMaster" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>级别：</label>
              <dict-tag :options="mobile_user_level" :value="detail.level" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>门头照片：</label>
              <div
                v-if="detail.storefrontPhoto"
                class="storefront-photo-container"
              >
                <img
                  :src="detail.storefrontPhoto"
                  class="storefront-photo"
                  @click="previewImage(detail.storefrontPhoto)"
                />
              </div>
              <span v-else>暂无</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>运营状态：</label>
              <dict-tag
                :options="operating_status"
                :value="detail.operationState"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>注册时间：</label>
              <span>{{ parseTime(detail.createTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>入驻时间：</label>
              <span>{{ parseTime(detail.updateTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>到期时间：</label>
              <span>{{ parseTime(detail.expireTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>到期状态：</label>
              <dict-tag
                :options="expiration_status"
                :value="detail.expireState"
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 续费订单信息 -->
      <div class="info-section">
        <div class="section-title">
          续费订单信息
          <!-- <el-button
            type="primary"
            size="small"
            class="ml10"
            @click="handleRenewal"
            >续费</el-button
          > -->
        </div>
        <el-table :data="partnerRenewals" border style="width: 930px">
          <el-table-column
            prop="orderNo"
            label="订单编号"
            align="center"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="unitPrice"
            label="单价(元/年)"
            align="center"
            width="120"
          >
            <template #default="scope">
              <span>￥{{ scope.row.unitPrice || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="unit"
            label="计量单位"
            align="center"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="number"
            label="数量"
            align="center"
            width="80"
          ></el-table-column>
          <el-table-column
            prop="price"
            label="续费金额(元)"
            align="center"
            width="120"
          >
            <template #default="scope">
              <span>￥{{ scope.row.price || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="expireTime"
            label="到期时间"
            align="center"
            width="120"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.expireTime) || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="订单时间"
            align="center"
            width="120"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createBy"
            label="操作者帐号"
            align="center"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.createBy || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 续费弹窗 -->
    <el-dialog
      v-model="renewalDialogVisible"
      title="续费"
      width="900px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="renewalForm"
        :rules="renewalRules"
        ref="renewalFormRef"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单编号：" prop="orderNo">
              <el-input
                v-model="renewalForm.orderNo"
                placeholder="系统自动生成唯一编号"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="到期时间：" prop="expireTime">
              <el-date-picker
                v-model="renewalForm.expireTime"
                type="date"
                placeholder="选择到期时间"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单价：" prop="unitPrice">
              <el-input
                v-model="renewalForm.unitPrice"
                placeholder="请输入单价"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位：" prop="unit">
              <el-select
                v-model="renewalForm.unit"
                placeholder="选择单位"
                style="width: 100%"
              >
                <el-option label="年" value="年" />
                <el-option label="月" value="月" />
                <el-option label="日" value="日" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量：" prop="number">
              <el-input v-model="renewalForm.number" placeholder="请输入数量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="金额：" prop="price">
              <el-input v-model="renewalForm.price" placeholder="请输入金额" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelRenewal">返回</el-button>
          <el-button type="primary" @click="saveRenewal">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 驿站绑定弹窗 -->
    <el-dialog
      v-model="stationDialogVisible"
      title="绑定的驿站"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="station-content">
        <div v-if="stationList.length === 0" class="empty-station">
          暂无绑定的驿站
        </div>
        <div v-else class="station-list">
          <div
            v-for="(station, index) in stationList"
            :key="index"
            class="station-item"
          >
            <div class="station-info">
              <div class="station-name">{{ station.name }}</div>
              <div class="station-address">{{ station.address }}</div>
              <div class="station-contact">
                <span>联系人：{{ station.contact }}</span>
                <span>电话：{{ station.phone }}</span>
              </div>
            </div>
            <div class="station-status">
              <el-tag :type="station.status === '1' ? 'success' : 'danger'">
                {{ station.status === '1' ? '正常' : '停用' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="stationDialogVisible = false">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CooperationListDetail">
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getPartnerDetail, addPartnerOrder } from '@/api/operation/cooperation';

const { proxy } = getCurrentInstance();
const { mobile_user_level, yes_or_no, operating_status, expiration_status } =
  proxy.useDict(
    'mobile_user_level',
    'yes_or_no',
    'operating_status',
    'expiration_status'
  );
const route = useRoute();
const router = useRouter();

const loading = ref(true);
const detail = ref({});
const partnerRenewals = ref([]);

// 续费弹窗相关
const renewalDialogVisible = ref(false);
const renewalFormRef = ref();
const renewalForm = ref({
  orderNo: '',
  expireTime: '',
  unitPrice: '',
  unit: '年',
  number: '',
  price: '',
});

const renewalRules = ref({
  expireTime: [
    { required: true, message: '请选择到期时间', trigger: 'change' },
  ],
  unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
  unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
  number: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  price: [{ required: true, message: '请输入金额', trigger: 'blur' }],
});

// 驿站绑定弹窗相关
const stationDialogVisible = ref(false);
const stationList = ref([]);

onMounted(() => {
  const userId = route.query.id || route.params.id;
  if (userId) {
    getDetail(userId);
  } else {
    proxy.$modal.msgError('缺少用户ID参数');
    goBack();
  }
});

function getDetail(userId) {
  loading.value = true;
  getPartnerDetail(userId)
    .then((response) => {
      detail.value = response.data || {};
      // 处理续费订单数据
      if (
        response.data.partnerRenewals &&
        Array.isArray(response.data.partnerRenewals)
      ) {
        partnerRenewals.value = response.data.partnerRenewals;
      } else {
        partnerRenewals.value = [];
      }
    })
    .catch((error) => {
      proxy.$modal.msgError('获取详情失败');
      console.error('获取详情失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
}

function getAccountTypeText(type) {
  const typeMap = {
    1: '主帐号',
    0: '副帐号',
  };
  return typeMap[type] || '--';
}

function getLevelText(level) {
  const levelMap = {
    1: '合作商',
    2: '代理商',
    3: '普通用户',
  };
  return levelMap[level] || '--';
}

function handleRenewal() {
  renewalDialogVisible.value = true;
}

function saveRenewal() {
  renewalFormRef.value.validate((valid) => {
    if (valid) {
      // 重新获取详情数据
      const userId = route.query.id || route.params.id;
      addPartnerOrder({ userId, ...renewalForm.value }).then(() => {
        proxy.$modal.msgSuccess('续费成功');
        renewalDialogVisible.value = false;
        getDetail(userId);
      });
    }
  });
}

function cancelRenewal() {
  renewalDialogVisible.value = false;
  // 重置表单
  renewalFormRef.value?.resetFields();
}

function goBack() {
  router.go(-1);
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.detail-content {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.info-section {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
  padding-bottom: 8px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title::after {
  content: '';
  width: 4px;
  height: 15px;
  background: #409eff;
  position: absolute;
  left: -8px;
  top: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.storefront-photo-container {
  display: inline-block;
}

.storefront-photo {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s;
}

.storefront-photo:hover {
  border-color: #409eff;
}

.el-table {
  margin-top: 10px;
}

.el-table th {
  background-color: #f5f7fa;
}

.ml10 {
  margin-left: 10px;
}

/* 驿站绑定弹窗样式 */
.station-content {
  min-height: 200px;
}

.empty-station {
  text-align: center;
  color: #909399;
  padding: 40px 0;
}

.station-list {
  max-height: 400px;
  overflow-y: auto;
}

.station-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 10px;
  background: #fafafa;
}

.station-info {
  flex: 1;
}

.station-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.station-address {
  color: #606266;
  font-size: 14px;
  margin-bottom: 5px;
}

.station-contact {
  color: #909399;
  font-size: 12px;
}

.station-contact span {
  margin-right: 15px;
}

.station-status {
  margin-left: 15px;
}

.dialog-footer {
  text-align: right;
}
</style>
