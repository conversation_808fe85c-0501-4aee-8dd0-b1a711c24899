<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="时间段" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          format="YYYY-MM"
          value-format="YYYY-MM"
          style="width: 240px"
          @change="handleTimeChange"
        />
      </el-form-item>
      <el-form-item label="手机" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入昵称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >确定</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="profitSharingList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="帐号" align="center" prop="mobile" width="120" />
      <el-table-column
        label="手机号"
        align="center"
        prop="mobile"
        width="120"
      />
      <el-table-column
        label="昵称"
        align="center"
        prop="nickName"
        width="120"
      />
      <el-table-column label="级别" align="center" prop="level" width="100">
        <template #default="scope">
          <dict-tag :options="mobile_user_level" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column
        label="网点名称"
        align="center"
        prop="websiteName"
        width="200"
      />
      <el-table-column
        label="驿站名称"
        align="center"
        prop="stationName"
        width="200"
      />
      <el-table-column
        label="分润合计(元)"
        align="center"
        prop="totalProfit"
        width="120"
      >
        <template #default="scope">
          <span>￥{{ scope.row.totalProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="旧衣物订单累计分润(元)"
        align="center"
        prop="oldClothesOrderProfit"
        width="180"
      >
        <template #default="scope">
          <span>￥{{ scope.row.oldClothesOrderProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="团购订单累计分润(元)"
        align="center"
        prop="groupBuyOrderProfit"
        width="180"
      >
        <template #default="scope">
          <span>￥{{ scope.row.groupBuyOrderProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="商城订单累计分润金额(元)"
        align="center"
        prop="mallOrderProfit"
        width="200"
      >
        <template #default="scope">
          <span>￥{{ scope.row.mallOrderProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="批发商城订单累计分润(元)"
        align="center"
        prop="wholesaleMallOrderProfit"
        width="200"
      >
        <template #default="scope">
          <span>￥{{ scope.row.wholesaleMallOrderProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="合作商年费订单累计分润(元)"
        align="center"
        prop="partnerAnnualFeeOrderProfit"
        width="220"
      >
        <template #default="scope">
          <span>￥{{ scope.row.partnerAnnualFeeOrderProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="100"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部统计信息 -->
    <div class="summary-info">累计分润: ￥{{ totalProfit }}元</div>
  </div>
</template>

<script setup name="ProfitSharingStats">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { getProfitSharingStats } from '@/api/financial';

const { proxy } = getCurrentInstance();

const profitSharingList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);
const totalProfit = ref(2235);
const timeRange = ref([]);

// 字典数据
const { mobile_user_level } = proxy.useDict('mobile_user_level');

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    startTime: undefined,
    endTime: undefined,
    mobile: undefined,
    nickName: undefined,
    websiteId: undefined,
    stationId: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  getProfitSharingStats(queryParams.value)
    .then((response) => {
      profitSharingList.value = response.data?.page?.records || [];
      total.value = response?.data?.page?.total || 0;
      totalProfit.value = response.data?.totalProfitSum || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  timeRange.value = [];
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  queryParams.value.mobile = undefined;
  queryParams.value.nickName = undefined;
  queryParams.value.websiteId = undefined;
  queryParams.value.stationId = undefined;
  handleQuery();
}

function handleTimeChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.value.startTime = dates[0];
    queryParams.value.endTime = dates[1];
  } else {
    queryParams.value.startTime = undefined;
    queryParams.value.endTime = undefined;
  }
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}

function handleDetail(row) {
  // 跳转到详情页面
  proxy.$router.push({
    path: '/financial/profitSharingStats/details/' + row.id,
  });
}
</script>

<style scoped>
.summary-info {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
</style>
