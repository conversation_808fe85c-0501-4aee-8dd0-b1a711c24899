<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      name="iFile"
      :data="{
        groupId: 0,
        fileType: 'image',
      }"
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUpload"
      :before-remove="handleDelete"
      :show-file-list="false"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: fileList.length >= limit }"
      :style="{
        '--size': size,
        '--height': height,
        '--default-margin': '0 8px 8px 0',
        '--margin': limit > 1 ? '0 8px 8px 0' : '0',
      }"
    >
      <slot>
        <el-icon class="avatar-uploader-icon"><plus /></el-icon
      ></slot>
    </el-upload>

    <!-- 自定义文件列表显示 -->
    <div class="custom-file-list" v-if="fileList.length > 0">
      <div
        v-for="(file, index) in fileList"
        :key="index"
        class="file-item"
        :class="{ 'video-file': isVideoUrl(file.url) }"
        :style="{
          width: size,
          height: height || size,
          margin: limit > 1 ? '0 8px 8px 0' : '0',
        }"
      >
        <!-- 图片文件显示 -->
        <div v-if="!isVideoUrl(file.url)" class="image-container">
          <img
            :src="file.url"
            :alt="file.name"
            @click="handlePictureCardPreview(file)"
          />
          <div class="file-actions">
            <el-icon
              class="preview-icon"
              @click="handlePictureCardPreview(file)"
            >
              <zoom-in />
            </el-icon>
            <el-icon class="delete-icon" @click="handleDelete(file)">
              <delete />
            </el-icon>
          </div>
        </div>

        <!-- 视频文件显示 -->
        <div v-else class="video-container">
          <video
            :src="file.url"
            :poster="getVideoPoster(file.url)"
            @click="handlePictureCardPreview(file)"
            preload="metadata"
          >
            您的浏览器不支持视频播放
          </video>
          <div class="video-overlay">
            <el-icon class="play-icon" @click="handlePictureCardPreview(file)">
              <video-play />
            </el-icon>
          </div>
          <div class="file-actions">
            <el-icon
              class="preview-icon"
              @click="handlePictureCardPreview(file)"
            >
              <zoom-in />
            </el-icon>
            <el-icon class="delete-icon" @click="handleDelete(file)">
              <delete />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
      </template>
      的文件
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showPreview && previewType === 'image'"
      :url-list="srcList"
      show-progress
      :initial-index="currentIndex"
      @close="showPreview = false"
    />

    <!-- 视频预览 -->
    <el-dialog
      v-if="showPreview && previewType === 'video'"
      v-model="showPreview"
      title="视频预览"
      width="80%"
      center
      :before-close="() => (showPreview = false)"
    >
      <div class="video-preview-container">
        <video
          :src="currentVideoUrl"
          controls
          style="width: 100%; max-height: 70vh"
          preload="metadata"
        >
          您的浏览器不支持视频播放
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { getToken } from '@/utils/auth';
import { isExternal } from '@/utils/validate';
import { getCurrentInstance, ref, computed, watch } from 'vue';
import { ZoomIn, Delete, VideoPlay } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: [String, Object, Array],
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg', 'mp4']
  fileType: {
    type: Array,
    default: () => ['png', 'jpg', 'jpeg'],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  // 宽度
  size: {
    type: String,
    default: '100px',
  },
  // 高度
  height: {},
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(['update:modelValue']);
const number = ref(0);
const uploadList = ref([]);
const showPreview = ref(false);
const previewType = ref('image'); // 新增：预览类型 'image' 或 'video'
const srcList = ref([]);
const currentIndex = ref(0);
const currentVideoUrl = ref(''); // 新增：当前预览的视频URL
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadImgUrl = ref(
  import.meta.env.VITE_APP_BASE_API + '/platform/file/upload/image'
); // 上传的图片服务器地址
const headers = ref({ tokenplatform: getToken() });
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

// 判断文件是否为视频
function isVideoFile(fileName) {
  const videoExtensions = [
    'mp4',
    'avi',
    'mov',
    'wmv',
    'flv',
    'webm',
    'mkv',
    'm4v',
  ];
  const extension = fileName.split('.').pop().toLowerCase();
  return videoExtensions.includes(extension);
}

// 判断URL是否为视频
function isVideoUrl(url) {
  if (!url) return false;
  const videoExtensions = [
    'mp4',
    'avi',
    'mov',
    'wmv',
    'flv',
    'webm',
    'mkv',
    'm4v',
  ];
  const extension = url.split('.').pop().toLowerCase();
  return videoExtensions.includes(extension);
}

// 获取视频封面（如果有的话）
function getVideoPoster(url) {
  // 这里可以根据实际需求返回视频封面URL
  // 暂时返回空字符串，让浏览器自动生成第一帧作为封面
  return '';
}

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(',');
      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        if (typeof item === 'string') {
          if (item.indexOf(baseUrl) === -1 && !isExternal(item)) {
            item = { name: baseUrl + item, url: baseUrl + item };
          } else {
            item = { name: item, url: item };
          }
        }
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

// 上传前loading加载
function handleBeforeUpload(file) {
  let isValidFile = false;
  if (props.fileType.length) {
    let fileExtension = '';
    if (file.name.lastIndexOf('.') > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
    }
    isValidFile = props.fileType.some((type) => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    // 默认支持图片和视频
    isValidFile =
      file.type.indexOf('image') > -1 || file.type.indexOf('video') > -1;
  }
  if (!isValidFile) {
    proxy.$modal.msgError(
      `文件格式不正确，请上传${props.fileType.join('/')}格式文件!`
    );
    return false;
  }
  if (file.name.includes(',')) {
    proxy.$modal.msgError('文件名不正确，不能包含英文逗号!');
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading('正在上传文件，请稍候...');
  number.value++;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 1) {
    uploadList.value.push({ name: res.data, url: res.data });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.imageUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除文件
function handleDelete(file) {
  const findex = fileList.value.findIndex((f) => f.url === file.url);
  if (findex > -1) {
    fileList.value.splice(findex, 1);
    emit('update:modelValue', listToString(fileList.value));
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    emit('update:modelValue', listToString(fileList.value));
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError('上传文件失败');
  proxy.$modal.closeLoading();
}

// 预览
function handlePictureCardPreview(file) {
  const fileUrl = file.url || file.response?.data;

  if (isVideoUrl(fileUrl)) {
    // 视频预览
    previewType.value = 'video';
    currentVideoUrl.value = fileUrl;
    showPreview.value = true;
  } else {
    // 图片预览
    previewType.value = 'image';
    srcList.value = fileList.value.map((item) => item.url);
    currentIndex.value = srcList.value.indexOf(fileUrl);
    showPreview.value = true;
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = '';
  separator = separator || ',';
  for (let i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf('blob:') !== 0) {
      strs += list[i].url.replace(baseUrl, '') + separator;
    }
  }
  return strs != '' ? strs.substr(0, strs.length - 1) : '';
}
</script>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}

:deep(.el-upload-list) {
  .el-upload--picture-card {
    width: var(--size) !important;
    height: var(--height, var(--size)) !important;
  }
}
:deep(.el-upload--picture-card) {
  width: var(--size) !important;
  height: var(--height, var(--size)) !important;
}
// 自定义文件列表样式
.custom-file-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.file-item {
  position: relative;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #409eff;

    .file-actions {
      opacity: 1;
    }
  }

  &.video-file {
    .video-overlay {
      opacity: 1;
    }
  }
}

.image-container,
.video-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.video-container {
  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;

  .play-icon {
    font-size: 24px;
    color: white;
    cursor: pointer;
  }
}

.file-actions {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  padding: 5px;
  opacity: 0;
  transition: opacity 0.3s;
  display: flex;
  gap: 5px;

  .preview-icon,
  .delete-icon {
    color: white;
    cursor: pointer;
    font-size: 20px;
    padding: 2px;
    border-radius: 2px;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }

  .delete-icon:hover {
    color: #f56c6c;
  }
}

// 视频预览样式
.video-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}
</style>
