<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="优惠券编号" prop="couponsId">
        <el-input
          v-model="queryParams.couponsId"
          placeholder="请输入优惠券编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="优惠券开始时间" prop="beginTime">
        <el-date-picker
          v-model="queryParams.beginTime"
          type="datetime"
          placeholder="请选择开始时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="优惠券到期时间" prop="endTime">
        <el-date-picker
          v-model="queryParams.endTime"
          type="datetime"
          placeholder="请选择到期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="发行状态" prop="couponsState">
        <el-select
          v-model="queryParams.couponsState"
          placeholder="请选择发行状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in coupons_state_dict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="couponsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="优惠券编号" align="center" prop="couponsId" />
      <el-table-column label="优惠券名称" align="center" prop="couponsName" />
      <el-table-column
        label="发行数量（张）"
        align="center"
        prop="numberIssuances"
      />
      <el-table-column
        label="领取数量（张）"
        align="center"
        prop="numberReceive"
      />
      <el-table-column
        label="优惠券使用开始时间"
        align="center"
        prop="beginTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.beginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="优惠券使用到期时间"
        align="center"
        prop="endTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发行状态" align="center" prop="couponsState">
        <template #default="scope">
          <dict-tag
            :options="coupons_state_dict"
            :value="scope.row.couponsState"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="发行时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="200"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)"
            >详情</el-button
          >
          <el-button link type="primary" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-button link type="danger" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Coupons">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { useRouter } from 'vue-router';
import { listCoupons, delCoupons } from '@/api/operation/coupons';

const { proxy } = getCurrentInstance();
const router = useRouter();

const couponsList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);

// 字典数据
const { coupons_state_dict } = proxy.useDict('coupons_state_dict');

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    couponsId: undefined,
    beginTime: undefined,
    endTime: undefined,
    couponsState: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  listCoupons(queryParams.value)
    .then((response) => {
      couponsList.value = response.data?.records || [];
      total.value = response.data?.total || 0;
    })

    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}

function handleAdd() {
  router.push('/operation/coupons/details/add?mode=add');
}

function handleView(row) {
  const couponsId = row?.couponsId || row?.id;
  router.push(`/operation/coupons/details/${couponsId}`);
}

function handleEdit(row) {
  const couponsId = row?.couponsId || row?.id;
  router.push(`/operation/coupons/details/${couponsId}?mode=edit`);
}

function handleDelete(row) {
  const targetIds = row?.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除优惠券编号为"' + targetIds + '"的数据项？')
    .then(function () {
      return delCoupons(targetIds);
    })
    .then((response) => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch((error) => {
      proxy.$modal.msgError('删除失败');
      console.error('删除失败:', error);
    });
}
</script>

<style scoped>
.dialog-footer.is-center {
  display: flex;
  justify-content: center;
}
</style>
