import request from '@/utils/request';

// 获取资料管理列表
export function listDataManage(params) {
  return request({
    url: '/platform/dataManage/list',
    method: 'get',
    params,
  });
}

// 获取资料管理详情
export function getDataManage(id) {
  return request({
    url: `/platform/dataManage/${id}`,
    method: 'get',
  });
}

// 新增资料管理
export function addDataManage(data) {
  return request({
    url: '/platform/dataManage',
    method: 'post',
    data,
  });
}

// 修改资料管理
export function updateDataManage(data) {
  return request({
    url: '/platform/dataManage',
    method: 'put',
    data,
  });
}

// 删除资料管理
export function delDataManage(ids) {
  return request({
    url: `/platform/dataManage/${ids}`,
    method: 'delete',
  });
}
