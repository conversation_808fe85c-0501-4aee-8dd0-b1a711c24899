﻿<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane label="股权分配" name="equity"> </el-tab-pane>
      <el-tab-pane label="市值设置" name="market"> </el-tab-pane>
    </el-tabs>
    <equity-distribution v-if="activeTab === 'equity'" />
    <market-setting v-if="activeTab === 'market'" />
  </div>
</template>

<script setup name="MarketCapitalization">
import { ref } from 'vue';
import EquityDistribution from './components/equityDistribution.vue';
import MarketSetting from './components/setting.vue';

const activeTab = ref('equity');

function handleTabChange(tabName) {
  console.log('切换到tab:', tabName);
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
