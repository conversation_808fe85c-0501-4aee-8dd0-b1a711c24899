import request from '@/utils/request';

// 根据代理商id查询配置
export function getConfigByAgentId(agentId) {
  return request({
    url: `/platform/shareBenefit/${agentId}`,
    method: 'get',
  });
}

// 保存配置
export function saveConfig(data) {
  return request({
    url: '/platform/shareBenefit/edit',
    method: 'post',
    data,
  });
}

// 根据key查询配置
export function getConfigByKey(key) {
  return request({
    url: `/platform/shareBenefit/get/${key}`,
    method: 'get',
  });
}
// 根据key保存配置
export function saveConfigByKey(data) {
  return request({
    url: `/platform/shareBenefit/add`,
    method: 'post',
    data,
  });
}

// ========== 协议管理相关接口 ==========

// 获取协议列表
export function getAgreementList(params) {
  return request({
    url: '/platform/agreement/list',
    method: 'get',
    params,
  });
}

// 获取协议详情
export function getAgreementDetail(id) {
  return request({
    url: `/platform/agreement/${id}`,
    method: 'get',
  });
}

// 新增协议
export function addAgreement(data) {
  return request({
    url: '/platform/agreement/add',
    method: 'post',
    data,
  });
}

// 修改协议
export function updateAgreement(data) {
  return request({
    url: '/platform/agreement/edit',
    method: 'put',
    data,
  });
}

// 删除协议
export function deleteAgreement(ids) {
  return request({
    url: `/platform/agreement/${ids}`,
    method: 'delete',
  });
}
// ========== 广告管理相关接口 ==========

// 获取广告列表
export function getAdvertisingList(params) {
  return request({
    url: '/platform/advertising/list',
    method: 'get',
    params,
  });
}

// 新增广告
export function addAdvertising(data) {
  return request({
    url: '/platform/advertising/add',
    method: 'post',
    data,
  });
}

// 修改广告
export function editAdvertising(data) {
  return request({
    url: '/platform/advertising/edit',
    method: 'put',
    data,
  });
}

// ========== 服务设置相关接口 ==========

// 根据key查询服务设置
export function getSettingByKey(key) {
  return request({
    url: `/platform/setting/${key}`,
    method: 'get',
  });
}

// 新增/修改服务设置
export function addOrUpdateSetting(data) {
  return request({
    url: `/platform/setting/add`,
    method: 'post',
    data,
  });
}
