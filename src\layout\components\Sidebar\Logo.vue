<template>
  <div
    class="sidebar-logo-container"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark'
          ? variables.menuBackground
          : variables.menuLightBackground,
      height: collapse ? '50px' : '64px',
      padding: collapse && '10px 0',
    }"
  >
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1
          v-else
          class="sidebar-title"
          :style="{
            color:
              sideTheme === 'theme-dark'
                ? variables.logoTitleColor
                : variables.logoLightTitleColor,
          }"
        >
          {{ title }}
        </h1>
      </router-link>
      <router-link
        v-else
        key="expand"
        class="sidebar-logo-link sidebar-logo-link-full"
        to="/"
      >
        <!-- <img src="@/assets/logo/1.png" class="w-[153px]" /> -->
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import variables from '@/assets/styles/variables.module.scss';
import logo from '@/assets/logo/logo.png';
import useSettingsStore from '@/store/modules/settings';

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});

const title = import.meta.env.VITE_APP_TITLE;
const settingsStore = useSettingsStore();
const sideTheme = computed(() => settingsStore.sideTheme);
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 60px;
  line-height: 60px;
  background: transparent;
  text-align: center;
  overflow: hidden;
  padding: 10px 0;
  margin-bottom: 10px;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    & .sidebar-logo {
      width: 30px;
      height: 30px;
      vertical-align: middle;

      transition: all 0.3s;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      margin-left: 12px;
      color: var(--el-color-primary);
      font-weight: 600;
      line-height: 60px;
      font-size: 16px;
      font-family: 'PingFang SC', 'Helvetica Neue', Arial, Helvetica, sans-serif;
      vertical-align: middle;
      transition: all 0.3s;
      background: linear-gradient(120deg, var(--el-color-primary), #36cfc9);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0;
      width: 32px;
      height: 32px;
    }

    .sidebar-title {
      display: none;
    }
  }
}
</style>
