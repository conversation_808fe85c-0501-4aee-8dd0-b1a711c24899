<template>
  <div class="app-container" style="padding: 20px">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane
        v-for="item in activeList"
        :key="item.name"
        :label="item.label"
        :name="item.name"
      ></el-tab-pane>
    </el-tabs>
    <div class="overflowClass">
      <component :is="activeName"></component>
    </div>
  </div>
</template>
<script>
import profitSharing from './components/profitSharing.vue';
import userAgreement from './components/userAgreement.vue';
import advertisingSettings from './components/advertisingSettings.vue';
import serviceSettings from './components/serviceSettings.vue';
export default {
  components: {
    profitSharing,
    userAgreement,
    advertisingSettings,
    serviceSettings,
  },
  data() {
    return {
      name: 'World',
      activeName: 'profitSharing',
      activeList: [
        { name: 'profitSharing', label: '分润设置' },
        { name: 'userAgreement', label: '用户协议' },
        { name: 'advertisingSettings', label: '广告设置' },
        { name: 'serviceSettings', label: '服务设置' },
      ],
    };
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  text-align: center;
  margin-top: 100px;
}
.overflowClass {
  overflow-x: hidden;
  overflow-y: auto;
  height: calc(100vh - 200px);
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.overflowClass::-webkit-scrollbar {
  /* Chrome, Safari, Opera*/
  display: none;
}

:deep(.cardCalss) {
  width: 100%;
  margin-bottom: 20px;
  min-height: 350px;

  .cardHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
  }
  .cardHeaderLeft {
    display: flex;
    align-items: center;
    .block {
      width: 8px;
      height: 20px;
      background: #409eff;
      margin-right: 5px;
    }
    div {
      font-size: 14px;
    }
  }
}
</style>
