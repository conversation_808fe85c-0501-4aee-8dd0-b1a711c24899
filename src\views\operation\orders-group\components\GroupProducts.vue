<template>
  <div class="group-products">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="发团日期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入商品名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>
      <el-form-item label="社长手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入社长手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="productsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="卖家绑定名称"
        align="center"
        prop="supplierName"
      />
      <el-table-column
        label="商品名称"
        align="center"
        prop="productName"
        width="400"
      />
      <el-table-column label="商品规格属性" align="center" prop="specType">
        <template #default="scope">
          <dict-tag :options="spec_type" :value="scope.row.specType" />
        </template>
      </el-table-column>
      <el-table-column label="团购状态" align="center" prop="tgStatus">
        <template #default="scope">
          <dict-tag :options="group_status" :value="scope.row.tgStatus" />
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" prop="categoryName" />
      <el-table-column
        label="发团时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网点名称" align="center" prop="websiteName" />
      <el-table-column label="驿站名称" align="center" prop="stationName" />
      <el-table-column
        label="操作"
        align="center"
        width="120"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="GroupProducts">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { listGroupProducts } from '@/api/operation/ordersGroup';
import { useRouter } from 'vue-router';
const router = useRouter();
const { proxy } = getCurrentInstance();

const productsList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const total = ref(0);

// 字典数据
const { group_status, spec_type } = proxy.useDict('group_status', 'spec_type');

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    dateRange: [],
    productName: undefined,
    productType: 'TG',
    websiteName: undefined,
    stationName: undefined,
    mobile: undefined,
    startTime: undefined,
    endTime: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;

  // 处理时间范围
  if (queryParams.value.dateRange && queryParams.value.dateRange.length === 2) {
    queryParams.value.startTime = queryParams.value.dateRange[0];
    queryParams.value.endTime = queryParams.value.dateRange[1];
  }

  listGroupProducts(queryParams.value)
    .then((response) => {
      productsList.value = response.data?.records || [];
      total.value = response.data?.total || 0;
    })

    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryRef');
  queryParams.value.dateRange = [];
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
}

function handleView(row) {
  proxy.$router.push({
    path: '/operation/orders-group/details/groupProducts/' + row.productId,
  });
}
</script>

<style scoped>
.group-products {
  padding: 20px 0;
}
</style>
