<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
          @change="handleDateChange"
        />
      </el-form-item>

      <el-form-item label="手机" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="mallOrderList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="帐号" align="center" prop="mobile" width="120" />
      <el-table-column
        label="手机号"
        align="center"
        prop="mobile"
        width="120"
      />
      <el-table-column label="级别" align="center" prop="level" width="100">
        <template #default="scope">
          <dict-tag :options="mobile_user_level" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column
        label="订单编号"
        align="center"
        prop="orderNo"
        width="150"
      />
      <el-table-column
        label="订单金额(元)"
        align="center"
        prop="orderPrice"
        width="120"
      >
        <template #default="scope">
          <span>￥{{ scope.row.orderPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="订单时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="订单类别"
        align="center"
        prop="productType"
        width="150"
      >
        <template #default="scope">
          <dict-tag :options="order_type" :value="scope.row.productType" />
        </template>
      </el-table-column>
      <el-table-column label="网点名称" align="center" prop="websiteName" />
      <el-table-column label="驿站名称" align="center" prop="stationName" />
      <!-- <el-table-column
        label="打款状态"
        align="center"
        prop="paymentStatus"
        width="100"
      >
        <template #default="scope">
          <dict-tag
            :options="payment_status"
            :value="scope.row.paymentStatus"
          />
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        width="100"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部统计信息 -->
    <div class="summary-info">
      累计订单笔数: {{ totalCount }}笔 累计消费金额: ￥{{ totalAmount }}元
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="detail-content">
        <!-- 订单及买家信息 -->
        <div class="detail-section">
          <h3 class="section-title">订单及买家信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">续费订单编号:</span>
              <span class="value">{{ detailData.orderNo || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">商城类型:</span>
              <dict-tag
                :options="product_nature"
                :value="detailData.productType"
              />
            </div>
            <div class="detail-item">
              <span class="label">网点:</span>
              <span class="value">{{ detailData.websiteName || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">驿站:</span>
              <span class="value">{{ detailData.stationName || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">订单时间:</span>
              <span class="value">{{
                parseTime(detailData.createTime) || '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="label">帐号:</span>
              <span class="value">{{ detailData.mobile || '-' }}</span>
            </div>

            <div class="detail-item">
              <span class="label">手机:</span>
              <span class="value">{{ detailData.mobile || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 商家信息 -->
        <div class="detail-section">
          <h3 class="section-title">商家信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">帐号:</span>
              <span class="value">{{ detailData.supplierMobile || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">手机:</span>
              <span class="value">{{ detailData.supplierMobile || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">绑定名称:</span>
              <span class="value">{{
                detailData.shopSupplierName || '-'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 付款信息 -->
        <div class="detail-section">
          <h3 class="section-title">付款信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">付款金额:</span>
              <span class="value">{{
                detailData.payPrice ? `￥${detailData.payPrice}` : '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="label">帐户:</span>
              <span class="value">{{ detailData.mobile || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">帐号:</span>
              <span class="value">{{ detailData.mobile || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">打款状态:</span>
              <span class="value">
                <dict-tag
                  :options="order_status"
                  :value="detailData.orderStatus"
                />
              </span>
            </div>
            <div class="detail-item">
              <span class="label">打款时间:</span>
              <span class="value">{{
                parseTime(detailData.payTime) || '-'
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MallOrderFlow">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { getMallOrderFlowList } from '@/api/financial';

const { proxy } = getCurrentInstance();

const mallOrderList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);
const totalCount = ref(336);
const totalAmount = ref(2235);
const dateRange = ref([]);

// 详情弹窗相关
const detailDialogVisible = ref(false);
const detailData = ref({});

// 字典数据
const { mobile_user_level, order_type, order_status } = proxy.useDict(
  'mobile_user_level',
  'order_type',
  'order_status'
);

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    startTime: undefined,
    endTime: undefined,
    mobile: undefined,
    stationId: undefined,
    websiteId: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  getMallOrderFlowList(queryParams.value)
    .then((response) => {
      mallOrderList.value = response.data?.page?.records || [];
      total.value = response.data?.page?.total || 0;
      totalCount.value = response.data?.orderCount || 0;
      totalAmount.value = response.data?.totalAmount || 0;
    })

    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  dateRange.value = [];
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleDateChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.value.startTime = dates[0];
    queryParams.value.endTime = dates[1];
  } else {
    queryParams.value.startTime = undefined;
    queryParams.value.endTime = undefined;
  }
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}

function handleDetail(row) {
  detailData.value = {
    ...row,
  };
  detailDialogVisible.value = true;
}
</script>

<style scoped>
.summary-info {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 8px;
  position: relative;
}
.section-title::after {
  content: '';
  width: 4px;
  height: 15px;
  background: #409eff;
  position: absolute;
  left: -8px;
  top: 1px;
}
.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.detail-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.detail-item .value {
  color: #303133;
  flex: 1;
}

.dialog-footer {
  text-align: center;
}
</style>
