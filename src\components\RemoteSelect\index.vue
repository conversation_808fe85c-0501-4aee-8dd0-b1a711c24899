<template>
  <el-select
    v-model="selectedValue"
    filterable
    remote
    :remote-method="(e) => debounce(remoteMethod(e), 300)"
    :loading="loading"
    :placeholder="placeholder"
    :clearable="clearable"
    :multiple="multiple"
    @change="handleChange"
  >
    <el-option
      v-if="!hasMatchingOption && modelName"
      :value="modelValue"
      :label="modelName"
    />
    <el-option
      v-for="item in options"
      :key="item[valueKey]"
      :label="item[labelKey]"
      :value="item[valueKey]"
    >
      <div style="display: flex; align-items: center">
        <span>{{ item[labelKey] }}</span>
        <span
          v-if="extraKey"
          style="color: #909399; font-size: 12px; margin-left: 10px"
        >
          {{ item[extraKey] }}
        </span>
      </div>
    </el-option>
  </el-select>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, onMounted, computed } from 'vue';
import { debounce } from '@/utils/index';
import useAppStore from '@/store/modules/app';

const appStore = useAppStore();
const props = defineProps({
  // 选中的值
  modelValue: {
    type: [String, Number, Array],
    default: null,
  },
  // 选中的名称
  modelName: {
    type: String,
    default: '',
  },
  // 提示文本
  placeholder: {
    type: String,
    default: '请选择',
  },
  // 初始加载
  initialLoad: {
    type: Boolean,
    default: true,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 请求URL
  url: {
    type: String,
    required: true,
  },
  // 值字段名
  valueKey: {
    type: String,
    default: 'id',
  },
  // 标签字段名
  labelKey: {
    type: String,
    default: 'name',
  },
  // 额外显示字段名（可选）
  extraKey: {
    type: String,
    default: '',
  },
  // 额外的请求参数
  extraParams: {
    type: Object,
    default: () => ({}),
  },
  // 额外请求参数校验，如果需要必传某一个参数后在执行搜索就传这个参数
  extraParamsArr: {
    type: Array,
    default: () => [],
  },
  // 响应数据的路径（用于处理嵌套数据结构）
  responsePath: {
    type: String,
    default: 'rows',
  },
  // 分页参数名
  pageSizeKey: {
    type: String,
    default: 'pageSize',
  },
  // 页码参数名
  pageNumKey: {
    type: String,
    default: 'pageIndex',
  },
  // 搜索关键词参数名
  keywordKey: {
    type: String,
  },
  // 是否有默认选项 默认第一条
  hasDefault: {
    type: Boolean,
    default: false,
  },
  // 是否可清空
  clearable: {
    type: Boolean,
    default: true,
  },
  // 请求方法
  method: {
    type: String,
    default: 'get',
  },
});

const emit = defineEmits(['update:modelValue', 'change']);

// 是否首次请求
const isFirst = ref(true);

const loading = ref(false);
const options = ref([]);
const selectedValue = ref(props.modelValue);

// 计算是否有匹配的选项
const hasMatchingOption = computed(() => {
  if (props.multiple && Array.isArray(props.modelValue)) {
    // 多选模式：检查每个值是否都有匹配项
    return props.modelValue.every((value) =>
      options.value.some((item) => item[props.valueKey] === value)
    );
  } else {
    // 单选模式：检查值是否有匹配项
    return options.value.some(
      (item) => item[props.valueKey] === props.modelValue
    );
  }
});

// 使用防抖处理远程搜索
const remoteMethod = (query = '') => {
  loading.value = true;
  fetchData({ [props.keywordKey || props.labelKey]: query }).then(() => {
    loading.value = false;
  });
};

// 获取数据
function fetchData(
  params = {
    [props.keywordKey || props.labelKey]: '',
  }
) {
  // 如果有必传校验的情况
  if (props.extraParamsArr && props.extraParamsArr.length > 0) {
    const boo = props.extraParamsArr.some(
      (i) => props.extraParams[i] && props.extraParams[i] !== 0
    );

    if (!boo) {
      options.value = [];
      emit('update:modelValue', null);
      emit('update:modelName', null);
      return Promise.resolve();
    }
  }
  const requestParams = {
    [props.pageNumKey]: 1,
    [props.pageSizeKey]: 50,
    ...props.extraParams,
    ...params,
  };

  return appStore
    .getRemoteData({
      url: props.url,
      method: props.method,
      params: requestParams,
    })
    .then((res) => {
      // 处理嵌套数据结构
      const data = props.responsePath ? res[props.responsePath] : res;
      options.value = Array.isArray(data) ? data : [];
      if (props.hasDefault && isFirst.value && !props.multiple) {
        isFirst.value = false;
        const value = options.value[0][props.valueKey];
        if (value) {
          emit('update:modelValue', value);
          emit('change', value);
        }
      }
      return res;
    });
}

// 处理选择变化
function handleChange(value) {
  emit('update:modelValue', value);
  if (props.multiple) {
    // 多选模式：寻找所有选中的项信息
    if (Array.isArray(value) && value.length > 0) {
      const selectedItems = value
        .map((id) => options.value.find((item) => item[props.valueKey] === id))
        .filter(Boolean);
      emit('change', selectedItems);
      emit(
        'update:modelName',
        selectedItems?.map((item) => item[props.labelKey])?.join(',')
      );
    } else {
      emit('change', []);
      emit('update:modelName', '');
    }
  } else {
    // 单选模式：找到对应的完整信息
    const selectedItem = options.value?.find(
      (item) => item[props.valueKey] === value
    );
    emit('change', selectedItem || null);
    emit('update:modelName', selectedItem?.[props.labelKey] || null);
  }
}

// 监听modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    selectedValue.value = val;
  }
);
// 监听其他参数的变化
watch(
  () => props.extraParams,
  () => {
    remoteMethod();
  }
);

// 组件初始化时，如果有modelValue但options为空，需要加载对应数据
onMounted(() => {
  if (props.initialLoad) {
    loading.value = true;
    fetchData().then(() => {
      loading.value = false;
    });
  } else if (props.modelValue && !options.value.length) {
    // 如果是多选模式且有值，需要处理数组
    if (
      props.multiple &&
      Array.isArray(props.modelValue) &&
      props.modelValue.length > 0
    ) {
      loading.value = true;
      // 构建查询参数
      const queryParams = {
        [props.valueKey + 's']: props.modelValue.join(','),
      };
      fetchData(queryParams).then(() => {
        loading.value = false;
      });
    } else if (!props.multiple && props.modelValue) {
      // 单选模式
      loading.value = true;
      const queryParams = {
        [props.valueKey]: props.modelValue,
      };
      fetchData(queryParams).then(() => {
        loading.value = false;

        // 如果加载后仍然没有匹配项，但有modelName，则保持显示
        if (!hasMatchingOption.value && props.modelName) {
          selectedValue.value = props.modelValue;
        }
      });
    }
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-select__input-wrapper) {
  width: 100% !important;

  .is-default {
    width: 100% !important;
  }
}
</style>
