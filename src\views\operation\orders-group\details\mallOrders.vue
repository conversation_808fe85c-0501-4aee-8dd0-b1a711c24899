<template>
  <div class="pb50" v-loading="loading">
    <div class="app-container">
      <!--内容-->
      <div class="product-content">
        <!--基本信息-->
        <div class="common-form">基本信息</div>
        <div class="table-wrap">
          <el-row>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">订单号：</span>
                {{ detail.orderNo }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">买家：</span>
                {{ detail.mobile }}
                <span>用户ID：({{ detail.userId }})</span>
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">订单金额 (元)：</span>
                {{ detail.orderPrice }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">运费金额 (元)：</span>
                {{ detail.expressPrice }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16" v-if="detail.pointsMoney > 0">
                <span class="gray9">积分抵扣 (元)：</span>
                {{ detail.pointsMoney }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16" v-if="detail.balance > 0">
                <span class="gray9">余额抵扣 (元)：</span>
                {{ detail.balance }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">实付款金额 (元)：</span>
                {{ detail.payPrice }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">支付方式：</span>
                {{ getPayTypeText(detail.payType) }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">配送方式：</span>
                {{ getDeliveryTypeText(detail.deliveryType) }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">交易状态：</span>
                <dict-tag :options="order_status" :value="detail.orderStatus" />
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">网点名称：</span>
                {{ detail.websiteName }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">驿站名称：</span>
                {{ detail.stationName }}
              </div>
            </el-col>
            <el-col :span="5">
              <div class="pb16">
                <span class="gray9">供应商：</span>
                {{ detail.shopSupplierName }}
              </div>
            </el-col>
          </el-row>
        </div>

        <!--商品信息-->
        <div class="common-form mt16">商品信息</div>
        <div class="table-wrap">
          <el-table
            size="small"
            :data="detail.product"
            border
            style="width: 100%"
          >
            <el-table-column prop="productName" label="商品" width="400">
              <template #default="scope">
                <div class="product-info">
                  <div class="pic">
                    <img :src="scope.row.imagePath" />
                  </div>
                  <div class="info">
                    <div class="name">{{ scope.row.productName }}</div>
                    <div class="price">
                      <span
                        :class="{
                          'text-d-line': scope.row.isUserGrade == 1,
                          gray6: scope.row.isUserGrade != 1,
                        }"
                        >￥ {{ scope.row.productPrice }}</span
                      >
                      <span class="ml10" v-if="scope.row.isUserGrade == 1"
                        >会员折扣价：￥ {{ scope.row.gradeProductPrice }}</span
                      >
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="productNo" label="商品编码"></el-table-column> -->
            <el-table-column
              prop="productWeight"
              label="重量 (Kg)"
            ></el-table-column>
            <el-table-column prop="totalNum" label="购买数量">
              <template #default="scope">
                <p>x {{ scope.row.totalNum }}</p>
              </template>
            </el-table-column>
            <el-table-column prop="totalPrice" label="商品总价(元)">
              <template #default="scope">
                <p>￥ {{ scope.row.totalPrice }}</p>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 收货信息 -->
        <div v-if="detail.deliveryType == 10">
          <div class="common-form mt16">收货信息</div>
          <div class="table-wrap">
            <el-row>
              <el-col :span="25">
                <div class="pb16">
                  <span class="gray9">备注：</span>
                  {{ detail.buyerRemark }}
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 自提门店信息 -->
        <template v-if="detail.deliveryType == 20">
          <div class="common-form mt16">自提信息</div>
          <div class="table-wrap">
            <el-row>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">网点名称：</span>
                  {{ detail.websiteName }}
                </div>
              </el-col>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">驿站名称：</span>
                  {{ detail.stationName }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="25">
                <div class="pb16">
                  <span class="gray9">备注：</span>
                  {{ detail.buyerRemark }}
                </div>
              </el-col>
            </el-row>
          </div>
        </template>

        <!--无需发货-->
        <template v-if="detail.deliveryType == 30">
          <div class="common-form mt16">用户信息</div>
          <div class="table-wrap">
            <el-row>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">联系手机：</span>
                  {{ detail.mobile }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="25">
                <div class="pb16">
                  <span class="gray9">备注：</span>
                  {{ detail.buyerRemark }}
                </div>
              </el-col>
            </el-row>
          </div>
        </template>

        <!--付款信息-->
        <div class="table-wrap" v-if="detail.payStatus == 20">
          <div class="common-form mt16">付款信息</div>
          <div class="table-wrap">
            <el-row>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">应付款金额：</span>
                  {{ detail.payPrice }}
                </div>
              </el-col>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">支付方式：</span>
                  {{ getPayTypeText(detail.payType) }}
                </div>
              </el-col>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">支付流水号：</span>
                  {{ detail.transactionId }}
                </div>
              </el-col>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">付款状态：</span>
                  {{ getPayStatusText(detail.payStatus) }}
                </div>
              </el-col>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">付款时间：</span>
                  {{ detail.payTime }}
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!--发货信息-->
        <div
          v-if="
            detail.payStatus == 20 &&
            detail.deliveryType == 10 &&
            [20, 21].indexOf(detail.orderStatus) === -1
          "
        >
          <div class="common-form mt16">发货信息</div>
          <div class="table-wrap">
            <el-row>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">物流公司：</span>
                  {{ detail.expressCompany }}
                </div>
              </el-col>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">物流单号：</span>
                  {{ detail.expressNo }}
                </div>
              </el-col>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">发货状态：</span>
                  {{ getDeliveryStatusText(detail.deliveryStatus) }}
                </div>
              </el-col>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">发货时间：</span>
                  {{ detail.deliveryTime }}
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!--取消信息-->
        <div
          class="table-wrap"
          v-if="detail.orderStatus == 20 && detail.cancelRemark != ''"
        >
          <div class="common-form mt16">取消信息</div>
          <div class="table-wrap">
            <el-row>
              <el-col :span="5">
                <div class="pb16">
                  <span class="gray9">商家备注：</span>
                  {{ detail.cancelRemark }}
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!--虚拟物品发货-->
        <div
          v-if="
            detail.deliveryType == 30 &&
            detail.payStatus == 20 &&
            detail.orderStatus != 21 &&
            detail.orderStatus != 20
          "
        >
          <div class="common-form mt16">虚拟商品发货</div>
          <div class="table-wrap">
            <template v-if="detail.virtualContent">
              <el-row>
                <el-col :span="5">
                  <div class="pb16">
                    <span class="gray9">发货信息：</span>
                    {{ detail.virtualContent }}
                  </div>
                </el-col>
                <el-col :span="5">
                  <div class="pb16">
                    <span class="gray9">发货状态：</span>
                    <template v-if="detail.deliveryStatus == 20">
                      已发货
                    </template>
                  </div>
                </el-col>
                <el-col :span="5">
                  <div class="pb16">
                    <span class="gray9">发货时间：</span>
                    {{ detail.deliveryTime }}
                  </div>
                </el-col>
              </el-row>
            </template>
          </div>
        </div>
      </div>
      <div class="common-button-wrapper">
        <el-button @click="cancelFunc">返回上一页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="MallOrdersDetail">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { getMallOrderDetail } from '@/api/operation/ordersGroup';
const { proxy } = getCurrentInstance();
const { order_status } = proxy.useDict('order_status');
const route = useRoute();

const loading = ref(true);
const detail = ref({});

onMounted(() => {
  getDetail();
});

function getDetail() {
  // 模拟数据，实际应该从API获取
  loading.value = true;

  // 从路由参数获取订单ID
  const orderId = route.params.id;
  getMallOrderDetail(orderId).then((res) => {
    detail.value = res.data;
    loading.value = false;
  });
}

function getPayTypeText(payType) {
  const payTypeMap = {
    10: '余额支付',
    20: '微信支付',
  };
  return payTypeMap[payType] || '未知';
}

function getDeliveryTypeText(deliveryType) {
  const deliveryTypeMap = {
    10: '快递配送',
    20: '上门自提',
    30: '无需物流',
  };
  return deliveryTypeMap[deliveryType] || '未知';
}

function getPayStatusText(payStatus) {
  const payStatusMap = {
    10: '未付款',
    20: '已付款',
  };
  return payStatusMap[payStatus] || '未知';
}

function getDeliveryStatusText(deliveryStatus) {
  const deliveryStatusMap = {
    10: '未发货',
    20: '已发货',
  };
  return deliveryStatusMap[deliveryStatus] || '未知';
}

function getImageUrl(imageId) {
  // 这里应该根据实际的图片服务返回正确的URL
  return `https://example.com/images/${imageId}`;
}

function cancelFunc() {
  proxy.$router.back(-1);
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}
.pb50 {
}

.common-form {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  position: relative;
  &::after {
    content: '';
    width: 4px;
    height: 15px;
    background: #409eff;
    position: absolute;
    left: -8px;
    top: 5px;
  }
}

.table-wrap {
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.pb16 {
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}

.gray9 {
  color: #999;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-info .pic {
  width: 60px;
  height: 60px;
  margin-right: 10px;
}

.product-info .pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.product-info .info .name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.product-info .info .price {
  font-size: 14px;
  color: #ff6600;
}

.text-d-line {
  text-decoration: line-through;
  color: #999;
}

.gray6 {
  color: #666;
}

.ml10 {
  margin-left: 10px;
}

.common-button-wrapper {
  text-align: center;
  padding: 20px 0;
}
</style>
