import request from '@/utils/request';

// 新增移动端菜单
export function addMenu(data) {
  return request({
    url: '/platform/frontAccess/add',
    method: 'post',
    data,
  });
}

// 删除移动端菜单
export function delMenu(id) {
  return request({
    url: '/platform/frontAccess/delete?id=' + id,
    method: 'post',
  });
}

// 编辑移动端菜单
export function updateMenu(data) {
  return request({
    url: '/platform/frontAccess/edit',
    method: 'post',
    data,
  });
}

// 移动端菜单列表
export function listMenu(params) {
  return request({
    url: '/platform/frontAccess/index',
    method: 'get',
    params,
  });
}
