<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="标题" prop="articleTitle">
        <el-input
          v-model="queryParams.articleTitle"
          placeholder="请输入标题"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="标题" align="center" prop="articleTitle" />
      <el-table-column label="发布者" align="center" prop="mobile" />
      <el-table-column
        label="发布时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="阅读" align="center" prop="readingQuantity" />
      <el-table-column
        label="操作"
        align="center"
        width="160"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改资料管理对话框 -->
    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <el-form ref="dataRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="标题" prop="articleTitle">
              <el-input v-model="form.articleTitle" placeholder="请输入标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签" prop="flag">
              <el-input v-model="form.flag" placeholder="请输入标签" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布者" prop="mobile">
              <el-input
                v-model="form.mobile"
                placeholder="请输入发布者账号"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="createTime">
              <el-date-picker
                disabled
                v-model="form.createTime"
                type="datetime"
                placeholder="请选择发布时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="正文" prop="body">
              <Editor v-model="form.body" :min-height="300" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer is-center">
          <el-button type="primary" :loading="submitLoading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DataManage">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import {
  listDataManage,
  getDataManage,
  addDataManage,
  updateDataManage,
  delDataManage,
} from '@/api/system/dataManage';
import Editor from '@/components/Editor/index.vue';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const { proxy } = getCurrentInstance();

const dataList = ref([]);
const open = ref(false);

const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const submitLoading = ref(false);
const viewData = ref({});

const data = reactive({
  form: {
    id: undefined,
    articleTitle: undefined,
    body: undefined,
    userId: undefined,
    mobile: undefined,
    flag: undefined,
    createTime: undefined,
    readingQuantity: 0,
  },
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    articleTitle: undefined,
  },
  rules: {
    articleTitle: [
      { required: true, message: '标题不能为空', trigger: 'blur' },
    ],
    body: [{ required: true, message: '正文不能为空', trigger: 'blur' }],
    mobile: [{ required: true, message: '发布者不能为空', trigger: 'blur' }],
    createTime: [
      { required: true, message: '发布时间不能为空', trigger: 'change' },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  listDataManage(queryParams.value)
    .then((response) => {
      dataList.value = response.data?.records || [];
      total.value = response.data?.total || 0;
    })
    .catch((error) => {
      proxy.$modal.msgError('获取资料列表失败');
      console.error('获取资料列表失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  data.form = {
    id: undefined,
    articleTitle: undefined,
    body: undefined,
    userId: undefined,
    mobile: undefined,
    flag: undefined,
    createTime: undefined,
    readingQuantity: 0,
  };
  proxy.resetForm('dataRef');
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = '新增资料管理';
  // 设置默认发布时间为当前时间
  form.value.createTime = proxy.parseTime(new Date());
  form.value.mobile = userStore.mobile;
}

function handleUpdate(row) {
  reset();
  const dataId = row?.id || ids.value[0];
  getDataManage(dataId)
    .then((response) => {
      Object.assign(data.form, response.data);
      open.value = true;
      title.value = '修改资料管理';
    })
    .catch((error) => {
      proxy.$modal.msgError('获取资料详情失败');
      console.error('获取资料详情失败:', error);
    });
}

function submitForm() {
  proxy.$refs['dataRef'].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      const api = form.value.id ? updateDataManage : addDataManage;
      api(form.value)
        .then((response) => {
          proxy.$modal.msgSuccess(form.value.id ? '修改成功' : '新增成功');
          open.value = false;
          getList();
        })
        .catch((error) => {
          proxy.$modal.msgError('操作失败');
          console.error('操作失败:', error);
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}

function handleDelete(row) {
  const targetIds = row?.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除资料管理编号为"' + targetIds + '"的数据项？')
    .then(function () {
      return delDataManage(targetIds);
    })
    .then((response) => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch((error) => {
      proxy.$modal.msgError('删除失败');
      console.error('删除失败:', error);
    });
}
</script>

<style scoped>
.article-detail {
  padding: 20px;
}

.article-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.article-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.article-meta {
  display: flex;
  gap: 20px;
  color: #909399;
  font-size: 14px;
}

.article-content {
  line-height: 1.8;
  color: #606266;
}

.dialog-footer.is-center {
  display: flex;
  justify-content: center;
}
</style>
