import request from '@/utils/request';

// 获取小程序设置详情
export function getAppWxDetail(data) {
  return request({
    url: '/platform/appSetting/appWx/index',
    method: 'get',
    params: data,
  });
}

// 编辑小程序设置
export function editAppWx(data) {
  return request({
    url: '/platform/appSetting/appWx/edit',
    method: 'post',
    data,
  });
}

// 获取APP设置详情
export function getAppDetail(data) {
  return request({
    url: '/platform/appSetting/app/index',
    method: 'get',
    params: data,
  });
}

// 编辑APP设置
export function editApp(data) {
  return request({
    url: '/platform/appSetting/app/edit',
    method: 'post',
    data,
  });
}

// 获取支付方式设置详情
export function getPayDetail(data) {
  return request({
    url: '/platform/appSetting/app/pay',
    method: 'get',
    params: data,
  });
}

// 编辑支付方式设置
export function editPay(data) {
  return request({
    url: '/platform/appSetting/app/editPay',
    method: 'post',
    data,
  });
}

// 上传P12文件
export function uploadP12(formData) {
  return request({
    url: '/platform/appSetting/app/uploadP12',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
