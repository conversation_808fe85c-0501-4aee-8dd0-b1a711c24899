import request from '@/utils/request';

// 旧物回收列表
export function listRecycleSize(params) {
  return request({
    url: '/platform/recyclesize/completed/page',
    method: 'post',
    data: params,
  });
}
// 旧物回收详情
export function getRecycleSizeDetail(id) {
  return request({
    url: `/platform/recyclesize/completed/${id}`,
    method: 'get',
  });
}

// 旧衣物回收列表
export function listRecycleClothes(params) {
  return request({
    url: '/platform/recycleclothes/completed/page',
    method: 'post',
    data: params,
  });
}
// 旧衣物详情
export function getRecycleClothesDetail(id) {
  return request({
    url: `/platform/recycleclothes/completed/${id}`,
    method: 'get',
  });
}

// 家庭维修列表
export function listRecycleMaintain(params) {
  return request({
    url: '/platform/recyle/completed/page',
    method: 'post',
    data: params,
  });
}

// 家庭维修详情
export function getRecycleMaintainDetail(id) {
  return request({
    url: `/platform/recyle/completed/${id}`,
    method: 'get',
  });
}
