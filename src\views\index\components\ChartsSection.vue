﻿<template>
  <el-row :gutter="20">
    <el-col :span="12" :xs="24" :sm="24" :md="12">
      <div class="chart-card">
        <div class="chart-title">驿站注册</div>
        <div class="chart-container">
          <div ref="stationChart" class="chart"></div>
        </div>
      </div>
    </el-col>

    <el-col :span="12" :xs="24" :sm="24" :md="12">
      <div class="chart-card">
        <div class="chart-title">旧衣物回收 (件)</div>
        <div class="chart-container">
          <div ref="recycleChart" class="chart"></div>
        </div>
      </div>
    </el-col>

    <el-col :span="12" :xs="24" :sm="24" :md="12">
      <div class="chart-card">
        <div class="chart-title">会员注册</div>
        <div class="chart-container">
          <div ref="memberChart" class="chart"></div>
        </div>
      </div>
    </el-col>

    <el-col :span="12" :xs="24" :sm="24" :md="12">
      <div class="chart-card">
        <div class="chart-title">销售额 (元)</div>
        <div class="chart-container">
          <div ref="salesChart" class="chart"></div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import useAppStore from '@/store/modules/app';

const appStore = useAppStore();
watch(
  () => appStore.sidebar.opened,
  (newVal) => {
    setTimeout(() => {
      nextTick(() => {
        resizeCharts();
      });
    }, 120);
  }
);
// 图表数据
const chartData = {
  months: [
    '24-01',
    '24-02',
    '24-03',
    '24-04',
    '24-05',
    '24-06',
    '24-07',
    '24-08',
    '24-09',
    '24-10',
    '24-11',
    '24-12',
  ],
  values: [
    1332, 2350, 1890, 1210, 1890, 890, 1890, 1500, 1500, 1300, 1890, 890,
  ],
};

const stationChart = ref(null);
const recycleChart = ref(null);
const memberChart = ref(null);
const salesChart = ref(null);

// 存储图表实例
const chartInstances = ref({});

// 创建柱状图
const createBarChart = (container, color, title) => {
  if (!container) return null;

  const chart = echarts.init(container);

  const option = {
    title: {
      show: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: chartData.months,
      axisLine: {
        lineStyle: {
          color: '#e0e0e0',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
      max: 2500,
      interval: 500,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'solid',
        },
      },
    },
    series: [
      {
        name: title,
        type: 'bar',
        data: chartData.values,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: getGradientColor(color, 0) },
            { offset: 1, color: getGradientColor(color, 1) },
          ]),
        },
        barWidth: '60%',
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  chart.setOption(option);
  return chart;
};

// 获取渐变颜色
const getGradientColor = (color, offset) => {
  const colors = {
    red: ['#ff6b6b', '#ff8e8e'],
    blue: ['#4dabf7', '#74c0fc'],
    purple: ['#9775fa', '#b197fc'],
    green: ['#51cf66', '#69db7c'],
  };
  return colors[color] ? colors[color][offset] : '#409eff';
};

// 初始化所有图表
const initCharts = async () => {
  await nextTick();

  if (stationChart.value) {
    chartInstances.value.station = createBarChart(
      stationChart.value,
      'red',
      '驿站注册'
    );
  }
  if (recycleChart.value) {
    chartInstances.value.recycle = createBarChart(
      recycleChart.value,
      'blue',
      '旧衣物回收'
    );
  }
  if (memberChart.value) {
    chartInstances.value.member = createBarChart(
      memberChart.value,
      'purple',
      '会员注册'
    );
  }
  if (salesChart.value) {
    chartInstances.value.sales = createBarChart(
      salesChart.value,
      'green',
      '销售额'
    );
  }
};

// 重新调整图表大小
const resizeCharts = () => {
  Object.values(chartInstances.value).forEach((chart) => {
    if (chart) {
      console.log('resizeCharts');
      chart.resize();
    }
  });
};

// 销毁图表
const destroyCharts = () => {
  Object.values(chartInstances.value).forEach((chart) => {
    if (chart) {
      chart.dispose();
    }
  });
  chartInstances.value = {};
};

onMounted(() => {
  initCharts();

  // 监听窗口大小变化
  window.addEventListener('resize', resizeCharts);
});

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('resize', resizeCharts);
  // 销毁图表实例
  destroyCharts();
});
</script>

<style scoped>
.chart-card {
  background: #fff;
  border-radius: 8px;
  height: 280px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  text-align: left;
}

.chart-container {
  height: 200px;
  width: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
