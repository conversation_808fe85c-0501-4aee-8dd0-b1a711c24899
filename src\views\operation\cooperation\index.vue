<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="合作商入驻" name="cooperation"> </el-tab-pane>
      <el-tab-pane label="入驻申请" name="application"> </el-tab-pane>
      <CooperationList v-if="activeTab === 'cooperation'" />
      <CooperationApplication v-if="activeTab === 'application'" />
    </el-tabs>
  </div>
</template>

<script setup name="Cooperation">
import { ref } from 'vue';
import CooperationList from './components/CooperationList.vue';
import CooperationApplication from './components/CooperationApplication.vue';

const activeTab = ref('cooperation');

function handleTabClick(tab) {
  console.log('切换到标签页:', tab.props.name);
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
