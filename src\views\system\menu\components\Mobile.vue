<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll"
          >展开/折叠</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="menuList"
      row-key="accessId"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column
        prop="name"
        label="菜单名称"
        :show-overflow-tooltip="true"
        width="160"
      ></el-table-column>

      <el-table-column prop="sort" label="排序" width="60"></el-table-column>
      <el-table-column
        prop="path"
        label="权限标识"
        :show-overflow-tooltip="true"
      ></el-table-column>

      <el-table-column
        label="创建时间"
        align="center"
        width="160"
        prop="createTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="210"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >

          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" v-model="open" width="680px" append-to-body>
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="sort">
              <el-input-number
                v-model="form.sort"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="权限字符" prop="path">
              <el-input v-model="form.path" placeholder="请输入权限字符" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="submitLoading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MobileMenu">
import {
  addMenu,
  delMenu,
  listMenu,
  updateMenu,
} from '@/api/system/menu/mobile';
const { proxy } = getCurrentInstance();
const menuList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref('');
const menuOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);
const iconSelectRef = ref(null);
// 提交按钮loading状态
const submitLoading = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    name: undefined,
    visible: undefined,
  },
  rules: {
    name: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
    sort: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }],
    path: [{ required: true, message: '路由地址不能为空', trigger: 'blur' }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询菜单列表 */
function getList() {
  loading.value = true;
  listMenu(queryParams.value)
    .then((response) => {
      menuList.value = proxy.handleTree(response.data, 'accessId');
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 查询菜单下拉树结构 */
function getTreeselect() {
  menuOptions.value = [];
  listMenu().then((response) => {
    const menu = { accessId: 0, name: '主类目', children: [] };
    menu.children = proxy.handleTree(response.data, 'accessId');
    menuOptions.value.push(menu);
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    accessId: undefined,
    parentId: 0,
    name: undefined,
    icon: undefined,
    menuType: 'C',
    sort: undefined,
    isFrame: '1',
    isCache: '0',
    visible: '0',
    status: '0',
  };
  proxy.resetForm('menuRef');
}

/** 展示下拉图标 */
function showSelectIcon() {
  iconSelectRef.value.reset();
}

/** 选择图标 */
function selected(name) {
  form.value.icon = name;
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  getTreeselect();
  if (row != null && row.accessId) {
    form.value.parentId = row.accessId;
  } else {
    form.value.parentId = 0;
  }
  open.value = true;
  title.value = '添加菜单';
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  await getTreeselect();

  form.value = { ...row };
  open.value = true;
  title.value = '修改菜单';
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['menuRef'].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      if (form.value.accessId != undefined) {
        updateMenu(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess('修改成功');
            open.value = false;
            getList();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      } else {
        addMenu(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.name + '"的数据项?')
    .then(function () {
      return delMenu(row.accessId);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

getList();
</script>
