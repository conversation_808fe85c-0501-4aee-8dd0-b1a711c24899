<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增协议</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col> -->

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="agreementList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="排序号" align="center" width="80">
        <template #default="scope">
          {{
            (queryParams.pageIndex - 1) * queryParams.pageSize +
            scope.$index +
            1
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="agreementNo"
        label="协议编号"
        align="center"
        width="200"
      />
      <el-table-column
        prop="agreementName"
        label="协议名称"
        align="center"
        width="200"
      />
      <el-table-column
        prop="agreementPlace"
        label="引用位置"
        align="center"
        show-overflow-tooltip
      />

      <el-table-column
        label="操作"
        align="center"
        width="150px"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button type="text" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <!-- <el-button type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改协议对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="协议编号" prop="agreementNo">
              <el-input
                v-model="form.agreementNo"
                placeholder="请输入协议编号"
                autocomplete="off"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议名称" prop="agreementName">
              <el-input
                v-model="form.agreementName"
                placeholder="请输入协议名称"
                autocomplete="off"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="引用位置" prop="agreementPlace">
          <el-input
            v-model="form.agreementPlace"
            placeholder="请输入引用位置"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="协议内容" prop="agreementContent">
          <Editor
            v-model="form.agreementContent"
            placeholder="请输入协议内容"
            :minHeight="300"
          ></Editor>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="submitLoading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UserAgreement">
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import {
  getAgreementList,
  getAgreementDetail,
  addAgreement,
  updateAgreement,
  deleteAgreement,
} from "@/api/operation/business";

const { proxy } = getCurrentInstance();

// 响应式数据
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const agreementList = ref([]);
const title = ref("");
const open = ref(false);
const submitLoading = ref(false);
const formRef = ref();

// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  agreementName: null,
  agreementNo: null,
});

// 表单参数
const form = ref({
  id: null,
  agreementNo: null,
  agreementName: null,
  agreementContent: null,
  agreementPlace: null,
});

// 表单校验规则
const rules = reactive({
  agreementNo: [
    { required: true, message: "协议编号不能为空", trigger: "blur" },
  ],
  agreementName: [
    { required: true, message: "协议名称不能为空", trigger: "blur" },
  ],
  agreementContent: [
    { required: true, message: "协议内容不能为空", trigger: "blur" },
  ],
  agreementPlace: [
    { required: true, message: "引用位置不能为空", trigger: "blur" },
  ],
});

// 生命周期
onMounted(() => {
  getList();
});

// 方法
/** 查询协议列表 */
function getList() {
  loading.value = true;
  getAgreementList(queryParams)
    .then((response) => {
      agreementList.value = response.data.records;
      total.value = response.data.total || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value.id = null;
  form.value.agreementNo = null;
  form.value.agreementName = null;
  form.value.agreementContent = null;
  form.value.agreementPlace = null;
  proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageIndex = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加协议";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getAgreementDetail(id).then((response) => {
    form.value.id = response.data.id;
    form.value.agreementNo = response.data.agreementNo;
    form.value.agreementName = response.data.agreementName;
    form.value.agreementContent = response.data.agreementContent;
    form.value.agreementPlace = response.data.agreementPlace;
    open.value = true;
    title.value = "修改协议";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      if (form.value.id != null) {
        updateAgreement(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      } else {
        addAgreement(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const agreementIds = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除选中的协议数据项？")
    .then(function () {
      return deleteAgreement(agreementIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}
</style>
