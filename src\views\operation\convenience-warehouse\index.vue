<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          >处理</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="warehouseList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="事项编号" align="center" prop="eventNumber" />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="标签" align="center" prop="tagName" />
      <el-table-column label="发布者" align="center" prop="mobile" />
      <el-table-column
        label="发布时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类别" align="center" prop="category">
        <template #default="scope">
          <dict-tag :options="category_dict" :value="scope.row.category" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="state">
        <template #default="scope">
          <dict-tag :options="state_dict" :value="scope.row.state" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="160"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-if="scope.row.state === 0"
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >处理</el-button
          >
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改便民库对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form
        ref="warehouseRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事项编号" prop="eventNumber">
              <el-input
                v-model="form.eventNumber"
                placeholder="请输入事项编号"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="form.title"
                placeholder="请输入标题"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签" prop="tagName">
              <el-input v-model="form.tagName" placeholder="请输入标签" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布者" prop="mobile">
              <el-input
                v-model="form.mobile"
                placeholder="请输入发布者"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类别" prop="category">
              <el-select
                v-model="form.category"
                placeholder="请选择类别"
                disabled
              >
                <el-option
                  v-for="item in category_dict"
                  :key="parseInt(item.value)"
                  :label="item.label"
                  :value="parseInt(item.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="state" disabled>
              <el-select v-model="form.state" placeholder="请选择状态">
                <el-option
                  v-for="item in state_dict"
                  :key="parseInt(item.value)"
                  :label="item.label"
                  :value="parseInt(item.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="地址" prop="address">
              <el-input
                v-model="form.address"
                placeholder="请输入地址"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="事项" prop="event">
              <el-input
                disabled
                v-model="form.event"
                type="textarea"
                :rows="4"
                placeholder="请输入事项内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处理情况" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入处理情况"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer is-center">
          <el-button type="primary" :loading="submitLoading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ConvenienceWarehouse">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import {
  listConvenienceWarehouse,
  getConvenienceWarehouse,
  addConvenienceWarehouse,
  updateConvenienceWarehouse,
  delConvenienceWarehouse,
} from '@/api/operation/conveniencewarehouse';

const { proxy } = getCurrentInstance();

const warehouseList = ref([]);
const open = ref(false);

const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const submitLoading = ref(false);

// 字典数据
const { category_dict, state_dict } = proxy.useDict(
  'category_dict',
  'state_dict'
);

const data = reactive({
  form: {
    id: undefined,
    mobile: undefined,
    tagName: undefined,
    eventNumber: undefined,
    title: undefined,
    createTime: undefined,
    category: undefined,
    state: '0',
    address: undefined,
    event: undefined,
    remark: undefined,
    updateTime: undefined,
  },
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    title: undefined,
  },
  rules: {
    eventNumber: [
      { required: true, message: '事项编号不能为空', trigger: 'blur' },
    ],
    title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
    mobile: [{ required: true, message: '发布者不能为空', trigger: 'blur' }],
    category: [{ required: true, message: '类别不能为空', trigger: 'change' }],
    state: [{ required: true, message: '状态不能为空', trigger: 'change' }],
    address: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
    event: [{ required: true, message: '事项不能为空', trigger: 'blur' }],
  },
});

const { queryParams, form, rules } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  listConvenienceWarehouse(queryParams.value)
    .then((response) => {
      warehouseList.value = response.data?.records || [];
      total.value = response.data?.total || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  data.form = {
    id: undefined,
    mobile: undefined,
    tagName: undefined,
    eventNumber: undefined,
    title: undefined,
    createTime: undefined,
    category: undefined,
    state: '0',
    address: undefined,
    event: undefined,
    remark: undefined,
    updateTime: undefined,
  };
  proxy.resetForm('warehouseRef');
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function handleUpdate(row) {
  reset();
  const warehouseId = row?.id || ids.value[0];
  getConvenienceWarehouse(warehouseId)
    .then((response) => {
      Object.assign(data.form, response.data);
      open.value = true;
      title.value = '处理便民库';
    })
    .catch((error) => {
      proxy.$modal.msgError('获取便民库详情失败');
      console.error('获取便民库详情失败:', error);
    });
}

function submitForm() {
  proxy.$refs['warehouseRef'].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      updateConvenienceWarehouse({
        ...form.value,
        state: 1,
      })
        .then((response) => {
          proxy.$modal.msgSuccess('处理成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}

function handleDelete(row) {
  const targetId = row?.id || ids.value[0];
  proxy.$modal
    .confirm('是否确认删除便民库编号为"' + targetId + '"的数据项？')
    .then(function () {
      return delConvenienceWarehouse(targetId);
    })
    .then((response) => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    });
}
</script>

<style scoped>
.warehouse-detail {
  padding: 20px;
}

.detail-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.detail-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.detail-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  color: #909399;
  font-size: 14px;
}

.detail-content {
  line-height: 1.8;
}

.content-item {
  margin-bottom: 15px;
}

.content-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
}

.content-item p {
  margin: 5px 0;
  color: #606266;
  white-space: pre-wrap;
}

.dialog-footer.is-center {
  display: flex;
  justify-content: center;
}
</style>
