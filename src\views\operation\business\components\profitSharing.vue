<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="18">
        <el-row :gutter="20">
          <el-col
            v-for="(item, index) in cardList"
            :key="index"
            :xs="24"
            :md="12"
            :lg="8"
          >
            <el-card class="cardCalss">
              <template #header>
                <div class="cardHeader">
                  <div class="cardHeaderLeft">
                    <div class="block"></div>
                    <div>{{ item.title }}</div>
                  </div>
                  <el-button type="primary" @click="save(item)">保存</el-button>
                </div></template
              >
              <el-form
                ref="ruleFormRef"
                style="max-width: 600px"
                :model="ruleForm"
                label-width="auto"
              >
                <el-form-item
                  v-for="(e, i) in item.formList"
                  :key="i"
                  :label="e.label"
                >
                  <el-input
                    v-model="item.form[e.prop]"
                    :disabled="e.prop === 'platformRatio'"
                    type="number"
                    @blur="blurInput(index, e.prop, item.form[e.prop])"
                  >
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      <el-col :xs="24" :md="12" :lg="6">
        <el-row :gutter="20">
          <el-col v-for="(item, index) in cardList2" :key="index" :span="24">
            <el-card class="cardCalss">
              <template #header>
                <div class="cardHeader">
                  <div class="cardHeaderLeft">
                    <div class="block"></div>
                    <div>{{ item.title }}</div>
                  </div>
                  <el-button type="primary" @click="saveOther(item)"
                    >保存</el-button
                  >
                </div></template
              >
              <el-form
                style="max-width: 600px"
                :model="ruleForm"
                label-width="auto"
              >
                <el-form-item
                  v-for="(e, i) in item.formList"
                  :key="i"
                  :label="e.label"
                >
                  <el-input
                    v-model="item.form[e.prop]"
                    type="number"
                    @blur="blurInput(index, e.prop, item.form[e.prop])"
                  >
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { sharingList, otherList } from './data.js';
import useUserStore from '@/store/modules/user';
import {
  getConfigByAgentId,
  saveConfig,
  saveConfigByKey,
  getConfigByKey,
} from '@/api/operation/business';
const { proxy } = getCurrentInstance();
const userStore = useUserStore();

// 响应式数据
const ruleForm = reactive({
  value: '',
});

const cardList = ref([...sharingList]);
// 供应链团购最低价限制 创业投资联盟
const cardList2 = ref([...otherList]);

// 获取配置数据
const getConfig = async () => {
  try {
    const res = await getConfigByAgentId(userStore.id);
    res.data.forEach((item) => {
      const targetItem = sharingList.find(
        (j) => item.orderType === j.orderType
      );
      if (targetItem) {
        targetItem.form = item;
      }
    });
    cardList.value = [...sharingList];
  } catch (error) {
    console.error('获取配置失败:', error);
  }
};

// 获取其他配置
const getOtherConfig = async () => {
  otherList.forEach((item, index) => {
    getConfigByKey(item.key).then((res) => {
      if (res.data) cardList2.value[index].form = res.data;
    });
  });
};
// 保存按钮
const save = (item) => {
  saveConfig(item.form)
    .then((res) => {
      proxy.$modal.msgSuccess('保存成功');
    })
    .catch((err) => {
      proxy.$modal.msgError('保存失败');
    });
};
//
// 保存其他配置
const saveOther = (item) => {
  saveConfigByKey({ key: item.key, ...item.form })
    .then((res) => {
      proxy.$modal.msgSuccess('保存成功');
    })
    .catch((err) => {
      proxy.$modal.msgError('保存失败');
    });
};
// input 失焦处理
const blurInput = (index, prop, value) => {
  if (Number(value) < 0) {
    cardList.value[index].form[prop] = 0;
  } else if (Number(value) > 100) {
    cardList.value[index].form[prop] = 100;
  }
};

// 生命周期
onMounted(() => {
  getConfig();
  getOtherConfig();
});
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 10px 15px;
  background: #eeeeee;
}
</style>
