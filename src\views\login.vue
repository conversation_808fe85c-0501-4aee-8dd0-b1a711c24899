<template>
  <div class="login-bg" :style="'background-image:url(' + bgImg + ');'">
    <img :src="logoImg" class="logo-img" />
    <img :src="supplierNameImg" class="login-title" />
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleFormRef"
      label-position="left"
      label-width="0px"
      class="demo-ruleForm login-container"
    >
      <h3 class="title" style="margin-bottom: 20px">账号密码登录</h3>
      <!--用户名-->
      <el-form-item prop="account"
        ><el-input
          type="text"
          v-model="ruleForm.account"
          auto-complete="off"
          placeholder="请输入账号"
        ></el-input
      ></el-form-item>
      <!--密码-->
      <el-form-item prop="checkPass"
        ><el-input
          type="password"
          v-model="ruleForm.checkPass"
          auto-complete="off"
          placeholder="请输入密码"
        ></el-input
      ></el-form-item>
      <!--验证码-->
      <el-form-item prop="captcha">
        <div class="captcha-container">
          <el-input
            type="text"
            v-model="ruleForm.captcha"
            auto-complete="off"
            placeholder="请输入验证码"
            class="captcha-input"
          ></el-input>
          <div class="captcha-code" @click="refreshCaptcha">
            <span
              v-for="(char, index) in captchaText"
              :key="index"
              :style="getCaptchaCharStyle(index)"
              class="captcha-char"
            >
              {{ char }}
            </span>
          </div>
        </div>
      </el-form-item>
      <!--登录-->
      <el-form-item
        ><el-button
          class="login-btn"
          type="primary"
          style="width: 100%"
          @click.native.prevent="SubmitFunc"
          :loading="loading"
          >登录</el-button
        ></el-form-item
      >
    </el-form>
  </div>
</template>

<script setup>
import bgImg from '@/assets/images/login-bg.png';
import supplierNameImg from '@/assets/images/logo-title.png';
import logoImg from '@/assets/images/logo.png';
import useUserStore from '@/store/modules/user';
import { reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const router = useRouter();
/*是否正在加载*/
const loading = ref(false);
/*表单对象*/
const ruleForm = ref({
  /*用户名*/
  account: '',
  /*密码*/
  checkPass: '',
  /*验证码*/
  captcha: '',
});
/*验证规则*/
const rules = reactive({
  /*用户名*/
  account: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  /*密码*/
  checkPass: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  /*验证码*/
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    {
      validator: validateCaptcha,
      trigger: 'blur',
    },
  ],
});
/*验证码相关*/
const captchaText = ref('');
const captchaValue = ref('');
const captchaColors = [
  '#FF6B6B',
  '#4ECDC4',
  '#45B7D1',
  '#96CEB4',
  '#FFEAA7',
  '#DDA0DD',
  '#98D8C8',
  '#F7DC6F',
];

/*生成验证码*/
function generateCaptcha() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  captchaText.value = result;
  captchaValue.value = result;
}
/*刷新验证码*/
function refreshCaptcha() {
  generateCaptcha();
  ruleForm.value.captcha = '';
}
/*获取验证码字符样式*/
function getCaptchaCharStyle(index) {
  const colors = captchaColors;
  const color = colors[index % colors.length];
  const rotation = (Math.random() - 0.5) * 30; // -15度到15度的随机旋转
  const fontSize = 18 + Math.random() * 4; // 18-22px的随机字体大小

  return {
    color: color,
    transform: `rotate(${rotation}deg)`,
    fontSize: `${fontSize}px`,
    fontWeight: 'bold',
    display: 'inline-block',
    margin: '0 2px',
    textShadow: '1px 1px 2px rgba(0,0,0,0.1)',
  };
}
/*验证码校验*/
function validateCaptcha(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入验证码'));
  } else if (value.toUpperCase() !== captchaValue.value.toUpperCase()) {
    callback(new Error('验证码错误'));
  } else {
    callback();
  }
}
/*登录方法*/
function SubmitFunc(ev) {
  proxy.$refs['ruleFormRef'].validate((valid) => {
    if (valid) {
      loading.value = true;
      const Params = {
        username: ruleForm.value.account,
        password: ruleForm.value.checkPass,
        captcha: ruleForm.value.captcha,
      };
      // 调用action的登录方法
      userStore
        .login(Params)
        .then(() => {
          localStorage.setItem('loginInfo', JSON.stringify(Params));
          router.push('/');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

onMounted(() => {
  generateCaptcha();
});
</script>

<style lang="scss" scoped>
.login-bg {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.login-container {
  border-radius: 10px;
  -moz-border-radius: 10px;
  background-clip: padding-box;
  padding: 35px 70px;
  background: #fff;
  .title {
    margin: 0px auto 40px auto;
    text-align: center;
    color: #222222;
    font-size: 28px;
    font-weight: 400;
  }
  .remember {
    margin: 0px 0px 35px 0px;
  }
}
.logo-img {
  width: 100%;
  height: 70px;
  object-fit: contain;
}
.login-title {
  width: 100%;
  height: 41px;
  object-fit: contain;
  margin-top: 20px;
  margin-bottom: 50px;
}

:deep(.el-input__wrapper) {
  border-radius: 10px;
  box-shadow: none;
  border: 2px solid transparent;
  width: 400px;
  height: 60px;
  background: #f7f7f7;
  font-size: 18px;
}
:deep(.el-input__wrapper.is-focus) {
  border: 2px solid #ef6901;
}
.login-btn {
  width: 400px;
  height: 60px;
  background: #ef6901;
  border-radius: 10px;
  color: #fff;
  font-weight: 500;
  font-size: 18px;
  border: none;
}

/* 验证码样式 */
.captcha-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.captcha-input {
  flex: 1;
}

.captcha-code {
  width: 120px;
  height: 60px;
  background: #f3f3f3;
  border: 2px solid transparent;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.captcha-code:hover {
  border-color: #ef6901;
  background: #f0f0f0;
}

.captcha-char {
  font-family: 'Arial', sans-serif;
  letter-spacing: 2px;
  transition: all 0.3s ease;
}

.captcha-code::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(0, 0, 0, 0.05) 2px,
    rgba(0, 0, 0, 0.05) 4px
  );
  pointer-events: none;
}
</style>
