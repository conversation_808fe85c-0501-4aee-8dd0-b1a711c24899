import request from '@/utils/request';

// 获取股权分配用户列表
export function listEquityUsers(params) {
  return request({
    url: '/platform/marketValue/list',
    method: 'get',
    params,
  });
}

// 获取用户股权变动记录
export function getEquityHistory(userId) {
  return request({
    url: `/platform/shareAllocation/list`,
    method: 'get',
    params: {
      userId,
    },
  });
}

// 新增股权变动
export function addEquityChange(data) {
  return request({
    url: '/platform/shareAllocation',
    method: 'post',
    data,
  });
}
