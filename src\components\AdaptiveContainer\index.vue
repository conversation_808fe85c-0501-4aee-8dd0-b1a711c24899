<template>
  <div id="container" :class="{ 'overflow-auto': scrollable }">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import autofit from 'autofit.js';
import { onMounted, onUnmounted } from 'vue';

interface Props {
  scrollable?: boolean;
}

withDefaults(defineProps<Props>(), {
  scrollable: false,
});

// 组件挂载时，添加窗口大小变化的事件监听
onMounted(() => {
  autofit.init({
    el: '#container',
    dw: 1920,
    dh: 1080,
    resize: true,
  });
});

// 组件卸载时，移除事件监听
onUnmounted(() => {
  autofit.off();
});
</script>

<style scoped>
.overflow-auto {
  overflow: auto !important;
}
</style>
