@import './variables.module.scss';

@mixin colorBtn($color) {
  background: $color;

  &:hover {
    color: $color;

    &:before,
    &:after {
      background: $color;
    }
  }
}

.blue-btn {
  @include colorBtn($blue);
}

.light-blue-btn {
  @include colorBtn($light-blue);
}

.red-btn {
  @include colorBtn($red);
}

.pink-btn {
  @include colorBtn($pink);
}

.green-btn {
  @include colorBtn($green);
}

.tiffany-btn {
  @include colorBtn($tiffany);
}

.yellow-btn {
  @include colorBtn($yellow);
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;

  &:hover {
    background: #fff;

    &:before,
    &:after {
      width: 100%;
      transition: 600ms ease all;
    }
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 2px;
    width: 0;
    transition: 400ms ease all;
  }

  &::after {
    right: inherit;
    top: inherit;
    left: 0;
    bottom: 0;
  }
}
// 自定义按钮样式
.custom-btn {
  background: #FFFFFF;
  color: #425C96;
  border: 1px solid #CFCFCF;
  height: 28px;
  line-height: 28px;
  min-width: 100px;
  &:hover {
    background-color: #DBE4F5;
    color: #0B388D;
    border: 1px solid #0B388D;
  }
}

// 重置按钮
.reset-btn {
  background-color: #dbe4f5;
  color: #0b388d;
  &:hover {
    background-color: #dbe4f5;
    color: #0b388d;
    border: 1px solid #0b388d;
  }
}

// 取消按钮
.cancel-btn {
  background-color: #DBDBDB;
  color: #425C96;
  &:hover {
    background-color: #DBDBDB;
    color: #425C96;
    border: 1px solid #425C96;
  }
}
