import request from '@/utils/request';

// 提现打款相关接口
export function getWithdrawalList(query) {
  return request({
    url: '/platform/payment/list',
    method: 'get',
    params: query,
  });
}

export function processWithdrawal(id) {
  return request({
    url: `/platform/payment/${id}`,
    method: 'get',
  });
}

// 旧衣物回收流水
export function getRecycleFlowList(query) {
  return request({
    url: '/platform/recycleclothes/completed/page',
    method: 'post',
    data: { ...query, orderState: 6 },
  });
}

// 合作商交纳年费流水
export function getPartnerAnnualFeeList(query) {
  return request({
    url: '/platform/user/getRecycleAndRepairPartners',
    method: 'post',
    data: query,
  });
}

// 商城订单流水 -已完成订单
export function getMallOrderFlowList(query) {
  return request({
    url: '/platform/order/page',
    method: 'post',
    data: { ...query, orderStatus: 30 },
  });
}

// 投资者充值流水 -已完成的会员卡订单
export function getInvestorRechargeList(query) {
  return request({
    url: '/platform/order/page',
    method: 'post',
    data: { ...query, orderStatus: 30, productType: 'HY' },
  });
}

// 会员卡核销流水
export function getMembershipCardFlowList(query) {
  return request({
    url: '/plateform/financial/membership/card-flow/list',
    method: 'get',
    params: query,
  });
}

// 维修流水
export function getMaintenanceFlowList(query) {
  return request({
    url: '/platform/recyle/completed/page',
    method: 'post',
    data: { ...query, orderState: 6 },
  });
}

// 团购订单流水
export function getGroupOrderFlowList(query) {
  return request({
    url: '/platform/order/page',
    method: 'post',
    data: { ...query, orderStatus: 30, productType: 'TG' },
  });
}

// 分润统计
export function getProfitSharingStats(query) {
  return request({
    url: '/platform/share/front/page',
    method: 'post',
    data: query,
  });
}

// 代理商分润统计
export function getAgentProfitSharingStats(query) {
  return request({
    url: '/plateform/financial/agent/profit-sharing/stats',
    method: 'get',
    params: query,
  });
}
