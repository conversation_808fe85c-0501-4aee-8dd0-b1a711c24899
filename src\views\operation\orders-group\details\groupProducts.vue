﻿<template>
  <div class="app-container">
    <div class="page-header">
      <h2>团购订单详情</h2>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 团购订单详情 -->
      <div class="info-section">
        <div class="section-title">团购订单详情</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>供货商名称：</label>
              <span>{{ orderDetail.supplierName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>团购状态：</label>
              <dict-tag
                :options="group_status"
                :value="orderDetail.tgStatus"
              ></dict-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>供应结算价：</label>
              <span>{{ orderDetail.supplierPrice || '--' }} 元</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>团购时间起：</label>
              <span>{{ parseTime(orderDetail.startTime) || '--' }} </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>团购时间止：</label>
              <span>{{ parseTime(orderDetail.endTime) || '--' }} </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>团购标题：</label>
              <span>{{ orderDetail.name || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>团购发布时间：</label>
              <span>{{ parseTime(orderDetail.createTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>总结算价：</label>
              <span>{{ orderDetail.totalPrice || '--' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 团购商品信息 -->
      <div class="info-section">
        <div class="section-title">团购商品信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>团购商品名称：</label>
              <span>{{ orderDetail.productName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>规格：</label>
              <span>{{ orderDetail.productName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>预估重量：</label>
              <span>{{ orderDetail.totalNum || '--' }}</span>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="info-item">
              <label>商品说明：</label>
              <span>{{ parseTime(orderDetail.createTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>供应时间：</label>
              <span>{{ parseTime(orderDetail.appointBeginTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>加工范围：</label>
              <span>{{ orderDetail.processRange || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>保质期：</label>
              <span>{{ orderDetail.shelfLife || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>出厂日期：</label>
              <span>{{ orderDetail.factoryTime || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>到期日期：</label>
              <span>{{ orderDetail.endTime || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>存储要求：</label>
              <span>{{ orderDetail.saveNeed || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>运输要求：</label>
              <span>{{ orderDetail.transportNeed || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>图片/视频：</label>
              <span>{{ '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>团购计划数量：</label>
              <span>{{ orderDetail.num || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>征集数量：</label>
              <span>{{ orderDetail.collectNum || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>供应商发布时间：</label>
              <span>{{ parseTime(orderDetail.createTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="info-item">
              <label>发货点：</label>
              <span>{{ orderDetail.address || '--' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 接龙情况 -->
      <div class="info-section">
        <div class="section-title">接龙情况</div>
        <el-table :data="relayList" border>
          <el-table-column label="订单号" align="center" prop="orderNo" />
          <el-table-column label="买家昵称" align="center" prop="nickName">
            <template #default="scope">
              <span>{{ scope.row.nickName || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="买家手机号" align="center" prop="mobile">
            <template #default="scope">
              <span>{{ scope.row.mobile || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="预订数量（公斤）"
            align="center"
            prop="reservedQuantity"
          >
            <template #default="scope">
              <span>{{ scope.row.totalNum || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="预付款（元）"
            align="center"
            prop="prepayment"
          >
            <template #default="scope">
              <span>{{ scope.row.totalPrice || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="发货数量（公斤）"
            align="center"
            prop="shippedQuantity"
          >
            <template #default="scope">
              <span>{{ scope.row.shipmentNum || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="结算价（元）"
            align="center"
            prop="settlementPrice"
          >
            <template #default="scope">
              <span>{{ scope.row.sysMoney || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="接龙时间" align="center" prop="relayTime">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="交货时间" align="center" prop="deliveryTime">
            <template #default="scope">
              <span>{{ parseTime(scope.row.endTime) || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup name="GroupProductsDetail">
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getGroupProductDetail } from '@/api/operation/ordersGroup';

const { proxy } = getCurrentInstance();
const { group_status } = proxy.useDict('group_status');
const route = useRoute();
const router = useRouter();

const loading = ref(true);
const showImageViewer = ref(false);

const orderDetail = ref({});

// 接龙情况列表（根据实际数据结构调整）
const relayList = ref([]);

// 计算是否有图片
const hasImages = computed(() => {
  return (
    (orderDetail.value.image && orderDetail.value.image.length > 0) ||
    (orderDetail.value.contentImage &&
      orderDetail.value.contentImage.length > 0)
  );
});

onMounted(() => {
  const orderId = route.query.id || route.params.id;
  if (orderId) {
    getOrderDetail(orderId);
  } else {
    proxy.$modal.msgError('缺少订单ID参数');
    goBack();
  }
});

function getOrderDetail(orderId) {
  loading.value = true;
  getGroupProductDetail(orderId)
    .then((response) => {
      orderDetail.value = response.data || {};
      relayList.value = response.data.userProductList;
    })
    .catch((error) => {
      proxy.$modal.msgError('获取订单详情失败');
      console.error('获取订单详情失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
}

function goBack() {
  router.go(-1);
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.detail-content {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.info-section {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
  padding-bottom: 8px;
  position: relative;
  &::after {
    content: '';
    width: 4px;
    height: 15px;
    background: #409eff;
    position: absolute;
    left: -8px;
    top: 4px;
  }
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.image-section {
  margin-bottom: 20px;
}

.image-title {
  font-weight: 500;
  color: #606266;
  margin-bottom: 10px;
  font-size: 14px;
}

.image-gallery {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.image-item {
  cursor: pointer;
  border: 2px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-item:hover {
  border-color: #409eff;
}

.el-table {
  margin-top: 10px;
}

.el-table th {
  background-color: #f5f7fa;
}
</style>
