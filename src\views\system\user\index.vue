<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in user_level"
            :key="parseInt(item.value)"
            :label="item.label"
            :value="parseInt(item.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="绑定名称" prop="bindName">
        <el-input
          v-model="queryParams.bindName"
          placeholder="请输入绑定名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入昵称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="类型" align="center" prop="type">
        <template #default="scope">
          <dict-tag :options="user_level" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column
        label="代理商编号"
        align="center"
        prop="numberNo"
        v-if="showAgentColumns"
      />
      <el-table-column label="昵称" align="center" prop="nickname" />
      <el-table-column label="手机" align="center" prop="mobile" />
      <el-table-column label="绑定名称" align="center" prop="bindName" />
      <el-table-column
        label="地址"
        align="center"
        prop="address"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="注册时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="220"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="success"
            icon="Setting"
            @click="handleRole(scope.row)"
            >权限</el-button
          >
          <el-button
            v-if="scope.row.id !== 1"
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" v-model="open" width="980px" append-to-body>
      <el-form ref="userRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20" class="section-card">
          <el-col :span="12">
            <el-form-item label="头像" prop="avatarUrl">
              <ImageUpload v-model="form.avatarUrl" :limit="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="form.nickname" placeholder="请输入昵称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="form.mobile" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="form.password"
                type="password"
                show-password
                placeholder="请输入密码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型">
                <el-option
                  v-for="item in user_level"
                  :key="parseInt(item.value)"
                  :label="item.label"
                  :value="parseInt(item.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="代理商编号"
              prop="numberNo"
              v-if="form.type == 1"
            >
              <el-input
                v-model="form.numberNo"
                placeholder="请输入代理商编号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="主副账号"
              prop="isMaster"
              v-if="form.type == 1"
            >
              <el-select v-model="form.isMaster" placeholder="请选择主副账号">
                <el-option
                  v-for="item in yes_or_no"
                  :key="parseInt(item.value)"
                  :label="item.label"
                  :value="parseInt(item.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="绑定代理商编号"
              prop="bindNumberNo"
              v-if="form.type == 1 && form.isMaster == 1"
            >
              <el-input
                v-model="form.bindNumberNo"
                placeholder="请输入绑定代理商编号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <div class="section-card">
          <div class="section-title">绑定信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="绑定名称" prop="bindName">
                <el-input
                  v-model="form.bindName"
                  placeholder="请输入绑定名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地址" prop="address">
                <el-input v-model="form.address" placeholder="请输入地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="绑定负责人" prop="bindBy">
                <el-input
                  v-model="form.bindBy"
                  placeholder="请输入绑定负责人"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="负责人手机号" prop="bindByPhone">
                <el-input
                  v-model="form.bindByPhone"
                  placeholder="请输入负责人手机号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作" prop="createBy">
                <el-input disabled :value="userStore.name" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.createTime">
              <el-form-item label="注册时间" prop="createTime">
                <el-input disabled :value="parseTime(form.createTime)" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="section-card" v-if="form.type == 1">
          <div class="section-title">平台分润</div>
          <div class="ratio-grid">
            <div class="ratio-card">
              <div class="ratio-title">旧衣物回收订单分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.clothes"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
            <div class="ratio-card">
              <div class="ratio-title">商城零售订单分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.retail"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
            <div class="ratio-card">
              <div class="ratio-title">合作商年费订单分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.cooperation"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
            <div class="ratio-card">
              <div class="ratio-title">团购订单分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.tuan"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
            <div class="ratio-card">
              <div class="ratio-title">商城批发订单分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.wholesale"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
            <div class="ratio-card">
              <div class="ratio-title">会员卡消费核销订单分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.card"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
            <div class="ratio-card">
              <div class="ratio-title">维修上门费分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.maintain"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
            <div class="ratio-card">
              <div class="ratio-title">社区商城服务订单分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.housekeeping"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
            <div class="ratio-card">
              <div class="ratio-title">快递代拿代发分润</div>
              <div class="ratio-row">
                <span>平台：</span
                ><el-input-number
                  v-model="form.shareRatios.express"
                  :min="0"
                  :max="100"
                /><span>%</span>
              </div>
            </div>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer is-center">
          <el-button type="primary" :loading="submitLoading" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog title="权限管理" v-model="roleOpen" width="500px" append-to-body>
      <el-form ref="roleRef" :model="roleForm" label-width="80px">
        <el-form-item label="用户昵称">
          <span>{{ roleForm.nickname }}</span>
        </el-form-item>
        <el-form-item label="角色权限">
          <el-checkbox-group v-model="roleForm.roleIds">
            <el-checkbox
              v-for="role in roleOptions"
              :key="role.roleId"
              :label="role.roleId"
            >
              {{ role.roleName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer is-center">
          <el-button
            type="primary"
            :loading="roleSubmitLoading"
            @click="submitRoleForm"
          >
            确 定
          </el-button>
          <el-button @click="cancelRole">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import { ref, reactive, getCurrentInstance, onMounted, computed } from 'vue';
import {
  listUser,
  getUserDetail,
  addUser,
  updateUser,
  delUser,
  updateUserRole,
} from '@/api/system/user';
import { listRole } from '@/api/system/role';
import ImageUpload from '@/components/ImageUpload/index.vue';
import useUserStore from '@/store/modules/user';
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const { user_level, order_type, bind_name, yes_or_no } = proxy.useDict(
  'user_level',
  'order_type',
  'bind_name',
  'yes_or_no'
);

const userList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const submitLoading = ref(false);

// 权限管理相关
const roleOpen = ref(false);
const roleSubmitLoading = ref(false);
const roleOptions = ref([]);
const roleForm = reactive({
  userId: undefined,
  nickname: '',
  roleIds: [],
});

// 控制代理商相关列的显示
const showAgentColumns = computed(() => {
  return userList.value.some((user) => user.type == 1);
});

const data = reactive({
  form: {
    avatarUrl: undefined,
    nickname: undefined,
    mobile: undefined,
    type: undefined,
    numberNo: undefined,
    isMaster: undefined,
    bindNumberNo: undefined,
    bindName: undefined,
    bindBy: undefined,
    bindByPhone: undefined,
    bindByNo: undefined,
    address: undefined,
    // UI使用的分润比率映射
    shareRatios: {
      retail: 0,
      housekeeping: 0,
      tuan: 0,
      wholesale: 0,
      card: 0,
      maintain: 0,
      clothes: 0,
      express: 0,
      cooperation: 0,
    },
  },
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    nickname: undefined,
    type: undefined,
    bindName: undefined,
    mobile: undefined,
    numberNo: undefined,
  },
  rules: {
    type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
    nickname: [{ required: true, message: '昵称不能为空', trigger: 'blur' }],
    mobile: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  },
});

const { queryParams, form, rules } = toRefs(data);

onMounted(() => {
  getList();
  getRoleOptions();
});

function getRoleOptions() {
  listRole()
    .then((response) => {
      roleOptions.value = response.data || [];
    })
    .catch((error) => {
      console.error('获取角色列表失败:', error);
    });
}

function getList() {
  loading.value = true;
  listUser(queryParams.value)
    .then((response) => {
      userList.value = response.data?.userList?.records || [];
      total.value = response.data?.userList?.total || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  data.form = {
    avatarUrl: undefined,
    nickname: undefined,
    mobile: undefined,
    type: undefined,
    numberNo: undefined,
    isMaster: undefined,
    bindNumberNo: undefined,
    bindName: undefined,
    bindBy: undefined,
    bindByPhone: undefined,
    bindByNo: undefined,
    address: undefined,
    shareRatios: {
      retail: 0,
      housekeeping: 0,
      tuan: 0,
      wholesale: 0,
      card: 0,
      maintain: 0,
      clothes: 0,
      express: 0,
      cooperation: 0,
    },
  };
  proxy.resetForm('userRef');
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = '新增平台用户';
}

// 将UI的shareRatios映射为后端benefits数组
function mapRatiosToBenefits() {
  const m = {
    retail: 'LS',
    housekeeping: 'JZ',
    tuan: 'TG',
    wholesale: 'PF',
    card: 'HY',
    maintain: 'WX',
    clothes: 'YW',
    express: 'KD',
    cooperation: 'HZ',
  };
  const arr = [];
  Object.keys(data.form.shareRatios).forEach((k) => {
    const val = data.form.shareRatios[k];
    if (val !== undefined && val !== null) {
      arr.push({ orderType: m[k], platformRatio: val });
    }
  });
  return arr;
}

// 从后端benefits数组还原到UI shareRatios
function mapBenefitsToRatios(benefits = []) {
  const rm = {
    LS: 'retail',
    JZ: 'housekeeping',
    TG: 'tuan',
    PF: 'wholesale',
    HY: 'card',
    WX: 'maintain',
    YW: 'clothes',
    KD: 'express',
    HZ: 'cooperation',
  };
  const obj = {
    retail: 0,
    housekeeping: 0,
    tuan: 0,
    wholesale: 0,
    card: 0,
    maintain: 0,
    clothes: 0,
    express: 0,
    cooperation: 0,
  };
  (benefits || []).forEach((b) => {
    const key = rm[b.orderType];
    if (key) obj[key] = Number(b.platformRatio) || 0;
  });
  data.form.shareRatios = obj;
}

function handleUpdate(row) {
  reset();
  const userId = row?.id || ids.value[0];
  // 获取用户详情
  getUserDetail(userId)
    .then((response) => {
      if (response.code == 1) {
        // 修改的时候将密码置空
        response.data.password = '';
        const userData = response.data;
        Object.assign(data.form, JSON.parse(JSON.stringify(userData)));
        // 将后端benefits转为UI的shareRatios
        mapBenefitsToRatios(userData.benefits);
        open.value = true;
        title.value = '修改平台用户';
      } else {
        proxy.$modal.msgError(response.msg || '获取用户详情失败');
      }
    })
    .catch((error) => {
      proxy.$modal.msgError('获取用户详情失败');
      console.error('获取用户详情失败:', error);
    });
}

function submitForm() {
  proxy.$refs['userRef'].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      const payload = { ...form.value };
      payload.benefits = mapRatiosToBenefits();
      const api = payload.id ? updateUser : addUser;
      api(payload)
        .then(() => {
          proxy.$modal.msgSuccess(payload.id ? '修改成功' : '新增成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}

function handleDelete(row) {
  const targetIds = row?.id || ids.value;
  // 如果id是1（admin），提示不允许删除
  if (targetIds.length > 0 && targetIds?.includes(1)) {
    proxy.$modal.msgError('不能删除超级管理员');
    return;
  }
  proxy.$modal
    .confirm('是否确认删除平台用户编号为"' + targetIds + '"的数据项？')
    .then(function () {
      return delUser(targetIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

// 权限管理相关函数
function handleRole(row) {
  roleForm.userId = row.id;
  roleForm.nickname = row.nickname;
  // 回显当前用户的角色
  roleForm.roleIds = (row.roleList || []).map((role) => role.roleId);
  roleOpen.value = true;
}

function cancelRole() {
  roleOpen.value = false;
  roleForm.userId = undefined;
  roleForm.nickname = '';
  roleForm.roleIds = [];
}

function submitRoleForm() {
  if (!roleForm.roleIds || roleForm.roleIds.length === 0) {
    proxy.$modal.msgWarning('请至少选择一个角色');
    return;
  }

  roleSubmitLoading.value = true;
  updateUserRole({
    userId: roleForm.userId,
    roleIds: roleForm.roleIds,
  })
    .then((response) => {
      if (response.code == 1) {
        proxy.$modal.msgSuccess('权限更新成功');
        roleOpen.value = false;
        getList(); // 刷新列表以显示更新后的角色
      } else {
        proxy.$modal.msgError(response.msg || '权限更新失败');
      }
    })
    .catch((error) => {
      proxy.$modal.msgError('权限更新失败');
      console.error('权限更新失败:', error);
    })
    .finally(() => {
      roleSubmitLoading.value = false;
    });
}
</script>

<style scoped>
.section-card {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}
.section-title {
  font-weight: 600;
  color: #303133;
  margin: -4px 0 12px;
}
.ratio-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}
.ratio-card {
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 12px;
}
.ratio-title {
  font-size: 13px;
  color: #606266;
  margin-bottom: 8px;
}
.ratio-row {
  display: flex;
  align-items: center;
  gap: 8px;
}
.dialog-footer.is-center {
  display: flex;
  justify-content: center;
}
</style>
