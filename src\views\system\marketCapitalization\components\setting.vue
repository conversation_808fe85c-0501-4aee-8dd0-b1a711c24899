﻿<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="marketList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column
        label="总市值(亿元)"
        align="center"
        prop="totalMarketValueHundredMillion"
      >
        <template #default="scope">
          {{ formatHundredMillion(scope.row.marketValue) }}
        </template>
      </el-table-column>
      <el-table-column label="每股单价(元)" align="center" prop="unitPrice" />
      <el-table-column label="数量" align="center" prop="amount" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改市值对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="marketRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="年份" prop="year">
          <el-date-picker
            v-model="form.year"
            type="date"
            placeholder="请选择年份"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="每股单价" prop="unitPrice">
          <el-input
            v-model="form.unitPrice"
            type="number"
            placeholder="请输入每股单价"
            style="width: 100%"
          >
            <template #append>元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="发行数量" prop="amount">
          <el-input
            v-model="form.amount"
            placeholder="请输入发行数量"
            type="number"
            style="width: 100%"
          >
            <template #append>股</template>
          </el-input>
        </el-form-item>
        <el-form-item label="总市值" prop="marketValue">
          <el-input v-model="form.marketValue" disabled placeholder="自动计算">
            <template #append>元</template>
          </el-input>
          <span style="margin-left: 10px; color: #666; font-weight: bold">
            {{ form.totalMarketValueHundredMillion }} 亿元
          </span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer is-center">
          <el-button type="primary" :loading="submitLoading" @click="submitForm"
            >确定</el-button
          >
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MarketSetting">
import {
  ref,
  reactive,
  getCurrentInstance,
  onMounted,
  watch,
  toRefs,
} from 'vue';
import {
  listSetting,
  addSetting,
} from '@/api/system/marketCapitalization/setting';

const { proxy } = getCurrentInstance();

const marketList = ref([]);
const open = ref(false);
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const submitLoading = ref(false);

const data = reactive({
  form: {
    id: undefined,
    year: undefined,
    unitPrice: undefined,
    amount: undefined,
    marketValue: undefined,
    totalMarketValueHundredMillion: '0.00',
  },
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
  },
  rules: {
    year: [{ required: true, message: '年份不能为空', trigger: 'change' }],
    unitPrice: [
      { required: true, message: '每股单价不能为空', trigger: 'blur' },
    ],
    amount: [{ required: true, message: '发行数量不能为空', trigger: 'blur' }],
  },
});

const { queryParams, form, rules } = toRefs(data);

onMounted(() => {
  getList();
});

// 获取列表数据
function getList() {
  loading.value = true;
  listSetting()
    .then((response) => {
      marketList.value = response.data || [];
      //   total.value = response.total || 0;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

// 格式化亿元显示
function formatHundredMillion(value) {
  if (!value) return '0.00';
  return (value / 100000000).toFixed(2);
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  data.form = {
    id: undefined,
    year: undefined,
    unitPrice: undefined,
    amount: undefined,
    marketValue: undefined,
    totalMarketValueHundredMillion: '0.00',
  };
  proxy.resetForm('marketRef');
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = '新增市值';
}

function submitForm() {
  proxy.$refs['marketRef'].validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      const params = {
        year: data.form.year,
        unitPrice: data.form.unitPrice,
        amount: data.form.amount,
        marketValue: data.form.marketValue,
      };

      addSetting(params)
        .then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
          submitLoading.value = false;
        })
        .catch(() => {
          submitLoading.value = false;
        });
    }
  });
}

// 自动计算总市值
function calculateTotalMarketValue() {
  if (data.form.unitPrice && data.form.amount) {
    data.form.marketValue = (data.form.unitPrice * data.form.amount).toFixed(2);
    data.form.totalMarketValueHundredMillion = formatHundredMillion(
      data.form.marketValue
    );
  }
}

// 监听每股单价和发行数量变化，自动计算总市值
watch(
  [() => data.form.unitPrice, () => data.form.amount],
  () => {
    calculateTotalMarketValue();
  },
  { deep: true }
);
</script>

<style scoped>
.dialog-footer.is-center {
  display: flex;
  justify-content: center;
}
</style>
