import request from '@/utils/request';

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/platform/platformAccess/add',
    method: 'post',
    data,
  });
}

// 删除
export function delMenu(id) {
  return request({
    url: '/platform/platformAccess/delete?id=' + id,
    method: 'post',
  });
}

// 编辑菜单
export function updateMenu(data) {
  return request({
    url: '/platform/platformAccess/edit',
    method: 'post',
    data,
  });
}

// 列表
export function listMenu(params) {
  return request({
    url: '/platform/platformAccess/index',
    method: 'get',
    params,
  });
}
