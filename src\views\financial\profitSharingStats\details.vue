<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-left">
        <h2>分润详情</h2>
        <div class="time-info">
          <span class="time-label">时间段:</span>
          <span class="time-value">{{ timePeriod }}</span>
        </div>
        <div class="total-info">
          <span class="total-label">分润合计:</span>
          <span class="total-value">{{ totalProfit }} 元</span>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 用户信息 -->
      <div class="info-section">
        <div class="section-title">用户信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>帐号:</label>
              <span>{{ userInfo.account || '--' }}</span>
            </div>
            <div class="info-item">
              <label>绑定名称:</label>
              <span>{{ userInfo.boundName || '--' }}</span>
            </div>
            <div class="info-item">
              <label>所属代理商:</label>
              <span>{{ userInfo.agentName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>手机号:</label>
              <span>{{ userInfo.phone || '--' }}</span>
            </div>
            <div class="info-item">
              <label>负责人:</label>
              <span>{{ userInfo.contactPerson || '--' }}</span>
            </div>
            <div class="info-item">
              <label>所属网点:</label>
              <span>{{ userInfo.outletName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>昵称:</label>
              <span>{{ userInfo.nickname || '--' }}</span>
            </div>
            <div class="info-item">
              <label>负责人手机:</label>
              <span>{{ userInfo.contactPhone || '--' }}</span>
            </div>
            <div class="info-item">
              <label>所属驿站:</label>
              <span>{{ userInfo.stationName || '--' }}</span>
            </div>
            <div class="info-item">
              <label>级别:</label>
              <span>{{ userInfo.level || '--' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 旧衣物回收分润 -->
      <div class="profit-section">
        <div class="section-title">
          旧衣物回收分润
          <span class="section-total">2000.00元</span>
        </div>
        <el-table :data="recycleProfitList" border>
          <el-table-column
            label="订单编号"
            align="center"
            prop="orderNo"
            width="120"
          />
          <el-table-column
            label="订单标题"
            align="center"
            prop="orderTitle"
            width="150"
          />
          <el-table-column
            label="订单金额(元)"
            align="center"
            prop="orderAmount"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.orderAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="分润(元)"
            align="center"
            prop="profitAmount"
            width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.profitAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="订单时间"
            align="center"
            prop="orderTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.orderTime) || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 团购订单分润 -->
      <div class="profit-section">
        <div class="section-title">
          团购订单分润
          <span class="section-total">1000.00元</span>
        </div>
        <el-table :data="groupOrderProfitList" border>
          <el-table-column
            label="订单编号"
            align="center"
            prop="orderNo"
            width="120"
          />
          <el-table-column
            label="订单标题"
            align="center"
            prop="orderTitle"
            width="150"
          />
          <el-table-column
            label="订单金额(元)"
            align="center"
            prop="orderAmount"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.orderAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="分润(元)"
            align="center"
            prop="profitAmount"
            width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.profitAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="订单时间"
            align="center"
            prop="orderTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.orderTime) || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 零售商城订单分润 -->
      <div class="profit-section">
        <div class="section-title">
          零售商城订单分润
          <span class="section-total">1000.00元</span>
        </div>
        <el-table :data="retailMallProfitList" border>
          <el-table-column
            label="订单编号"
            align="center"
            prop="orderNo"
            width="120"
          />
          <el-table-column
            label="订单标题"
            align="center"
            prop="orderTitle"
            width="150"
          />
          <el-table-column
            label="订单金额(元)"
            align="center"
            prop="orderAmount"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.orderAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="分润(元)"
            align="center"
            prop="profitAmount"
            width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.profitAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="订单时间"
            align="center"
            prop="orderTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.orderTime) || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 批发商城订单分润 -->
      <div class="profit-section">
        <div class="section-title">
          批发商城订单分润
          <span class="section-total">1000.00元</span>
        </div>
        <el-table :data="wholesaleMallProfitList" border>
          <el-table-column
            label="订单编号"
            align="center"
            prop="orderNo"
            width="120"
          />
          <el-table-column
            label="订单标题"
            align="center"
            prop="orderTitle"
            width="150"
          />
          <el-table-column
            label="订单金额(元)"
            align="center"
            prop="orderAmount"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.orderAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="分润(元)"
            align="center"
            prop="profitAmount"
            width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.profitAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="订单时间"
            align="center"
            prop="orderTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.orderTime) || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 合作商年费订单分润 -->
      <div class="profit-section">
        <div class="section-title">
          合作商年费订单分润
          <span class="section-total">1000.00元</span>
        </div>
        <el-table :data="partnerFeeProfitList" border>
          <el-table-column
            label="订单编号"
            align="center"
            prop="orderNo"
            width="120"
          />
          <el-table-column
            label="订单标题"
            align="center"
            prop="orderTitle"
            width="150"
          />
          <el-table-column
            label="订单金额(元)"
            align="center"
            prop="orderAmount"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.orderAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="分润(元)"
            align="center"
            prop="profitAmount"
            width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.profitAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="订单时间"
            align="center"
            prop="orderTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.orderTime) || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 家庭维修(上门检查)订单分润 -->
      <div class="profit-section">
        <div class="section-title">
          家庭维修(上门检查)订单分润
          <span class="section-total">1000.00元</span>
        </div>
        <el-table :data="repairProfitList" border>
          <el-table-column
            label="订单编号"
            align="center"
            prop="orderNo"
            width="120"
          />
          <el-table-column
            label="订单标题"
            align="center"
            prop="orderTitle"
            width="150"
          />
          <el-table-column
            label="订单金额(元)"
            align="center"
            prop="orderAmount"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.orderAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="分润(元)"
            align="center"
            prop="profitAmount"
            width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.profitAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="订单时间"
            align="center"
            prop="orderTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.orderTime) || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup name="ProfitSharingDetails">
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const loading = ref(true);

// 用户信息
const userInfo = ref({});

// 时间段和总金额
const timePeriod = ref('2025-1到2025-6');
const totalProfit = ref('6000.00');

// 各类分润数据
const recycleProfitList = ref([]);
const groupOrderProfitList = ref([]);
const retailMallProfitList = ref([]);
const wholesaleMallProfitList = ref([]);
const partnerFeeProfitList = ref([]);
const repairProfitList = ref([]);

onMounted(() => {
  const userId = route.query.id || route.params.id;
  if (userId) {
    getProfitDetail(userId);
  } else {
    proxy.$modal.msgError('缺少用户ID参数');
    goBack();
  }
});

function getProfitDetail(userId) {
  loading.value = true;

  // 模拟数据，实际应该调用API
  setTimeout(() => {
    // 用户信息
    userInfo.value = {
      account: 'xsydn',
      boundName: '渝北区XX公司',
      agentName: '代理商用户绑定名称',
      phone: '***********',
      contactPerson: '渝北区龙山社区驿站',
      outletName: '网点用户绑定名称',
      nickname: '伍小大',
      contactPhone: '***********',
      stationName: '驿站用户绑定名称',
      level: '社长',
    };

    // 模拟各类分润数据
    const mockData = {
      orderNo: 'HS200294',
      orderTitle: '',
      orderAmount: '500.00',
      profitAmount: '50.00',
      orderTime: '2025-8-15 15:35:22',
    };

    recycleProfitList.value = Array(6)
      .fill()
      .map(() => ({ ...mockData }));
    groupOrderProfitList.value = Array(6)
      .fill()
      .map(() => ({ ...mockData }));
    retailMallProfitList.value = Array(6)
      .fill()
      .map(() => ({ ...mockData }));
    wholesaleMallProfitList.value = Array(6)
      .fill()
      .map(() => ({ ...mockData }));
    partnerFeeProfitList.value = [];
    repairProfitList.value = [];

    loading.value = false;
  }, 1000);
}

function goBack() {
  router.go(-1);
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-left h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.time-info,
.total-info {
  margin-bottom: 5px;
}

.time-label,
.total-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.time-value,
.total-value {
  color: #303133;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.help-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.help-icon .icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 5px;
}

.help-icon .text {
  font-size: 12px;
  color: #606266;
}

.detail-content {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.info-section {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
  padding-bottom: 8px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title::after {
  content: '';
  width: 4px;
  height: 15px;
  background: #409eff;
  position: absolute;
  left: -8px;
  top: 4px;
}

.section-total {
  color: #409eff;
  font-weight: 600;
  font-size: 14px;
}

.profit-section {
  margin-bottom: 30px;
}

.profit-section .section-title {
  background: #f5f7fa;
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 0;
  border: 1px solid #e4e7ed;
}

.profit-section .section-title::after {
  display: none;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.el-table {
  margin-top: 10px;
}

.el-table th {
  background-color: #f5f7fa;
}
</style>
