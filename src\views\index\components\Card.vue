<template>
  <el-row :gutter="20" class="mb-6">
    <el-col :span="6" :xs="24" :sm="12" :md="6">
      <div class="stat-card">
        <div class="stat-icon blue">
          <img
            src="@/assets/images/index/1.png"
            class="el-icon-office-building"
          />
        </div>
        <div class="stat-content-wrapper">
          <div class="stat-content">
            <div class="stat-title">
              <div>网点:</div>
              <div class="stat-value">{{ stats.outlets }}</div>
              个
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-title">
              <div>合作商：</div>
              <div class="stat-value">{{ stats.partners }}</div>
              家
            </div>
          </div>
        </div>
      </div>
    </el-col>

    <el-col :span="6" :xs="24" :sm="12" :md="6">
      <div class="stat-card">
        <div class="stat-icon blue">
          <img
            src="@/assets/images/index/2.png"
            class="el-icon-office-building"
          />
        </div>
        <div class="stat-content-wrapper">
          <div class="stat-content">
            <div class="stat-title">
              <div>网点:</div>
              <div class="stat-value">{{ stats.outlets }}</div>
              个
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-title">
              <div>合作商：</div>
              <div class="stat-value">{{ stats.partners }}</div>
              家
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="6" :xs="24" :sm="12" :md="6">
      <div class="stat-card">
        <div class="stat-icon blue">
          <img
            src="@/assets/images/index/3.png"
            class="el-icon-office-building"
          />
        </div>
        <div class="stat-content-wrapper">
          <div class="stat-content">
            <div class="stat-title">
              <div>网点:</div>
              <div class="stat-value">{{ stats.outlets }}</div>
              个
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-title">
              <div>合作商：</div>
              <div class="stat-value">{{ stats.partners }}</div>
              家
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="6" :xs="24" :sm="12" :md="6">
      <div class="stat-card">
        <div class="stat-icon blue">
          <img
            src="@/assets/images/index/4.png"
            class="el-icon-office-building"
          />
        </div>
        <div class="stat-content-wrapper">
          <div class="stat-content">
            <div class="stat-title">
              <div>网点:</div>
              <div class="stat-value">{{ stats.outlets }}</div>
              个
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-title">
              <div>合作商：</div>
              <div class="stat-value">{{ stats.partners }}</div>
              家
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>
<script setup>
import { ref } from 'vue';

// 统计数据
const stats = ref({
  outlets: 36,
  partners: 256,
  stations: 36,
  suppliers: 256,
  presidents: 36,
  wholesalers: 256,
  members: 36,
  stores: 256,
});
</script>
<style scoped>
.app-container {
  padding: 20px;
  background-color: #fff;
}

.stat-card {
  background: #fff;
  border-radius: 8px;

  display: flex;
  align-items: center;
  height: 100px;
  transition: all 0.3s ease;
}

.stat-content-wrapper {
  display: flex;
  flex-direction: column;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #fff;
}
.el-icon-office-building {
  width: 100%;
  height: 100%;
}

.stat-icon.blue {
  background: linear-gradient(135deg, #409eff, #66b3ff);
}

.stat-icon.orange {
  background: linear-gradient(135deg, #ff9500, #ffb84d);
}

.stat-icon.green {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-icon.yellow {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
}

.stat-content {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #222222;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.stat-value {
  font-size: 24px;
  color: #222222;
  margin-bottom: 4px;
  margin-right: 5px;
}

.stat-subtitle {
  font-size: 12px;
  color: #222222;
}
</style>
