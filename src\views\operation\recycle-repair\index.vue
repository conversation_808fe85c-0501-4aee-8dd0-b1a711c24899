<template>
  <div class="app-container">
    <!-- 统一的搜索表单 -->
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="订单编号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择关联区域"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="handleQuery"
      />
    </el-row>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="旧衣物回收" name="recycleClothes"> </el-tab-pane>
      <el-tab-pane label="旧物回收" name="recycleSize"> </el-tab-pane>
      <el-tab-pane label="家庭维修" name="recycleMaintain"> </el-tab-pane>
    </el-tabs>

    <!-- 组件内容 -->
    <div class="tab-content">
      <RecycleSize
        v-if="activeTab === 'recycleSize'"
        :query-params="processedQueryParams"
        ref="tabRef"
      />
      <RecycleClothes
        v-if="activeTab === 'recycleClothes'"
        :query-params="processedQueryParams"
        ref="tabRef"
      />
      <RecycleMaintain
        v-if="activeTab === 'recycleMaintain'"
        :query-params="processedQueryParams"
        ref="tabRef"
      />
    </div>
  </div>
</template>

<script setup name="RecycleRepair">
import { ref, reactive, getCurrentInstance, computed, toRefs } from 'vue';
import RecycleSize from './components/RecycleSize.vue';
import RecycleClothes from './components/RecycleClothes.vue';
import RecycleMaintain from './components/RecycleMaintain.vue';

const { proxy } = getCurrentInstance();

const activeTab = ref('recycleClothes');
const showSearch = ref(true);
const tabRef = ref(null);
const dateRange = ref([]);
const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    orderNo: undefined,
    websiteId: undefined,
    stationId: undefined,
    startTime: undefined,
    endTime: undefined,
  },
});

const { queryParams } = toRefs(data);

// 处理查询参数，将时间范围转换为开始和结束时间
const processedQueryParams = computed(() => {
  const params = { ...queryParams.value };
  // // 处理时间范围
  if (dateRange.value && dateRange.value.length === 2) {
    params.startTime = dateRange.value[0];
    params.endTime = dateRange.value[1];
  } else {
    params.startTime = undefined;
    params.endTime = undefined;
  }
  return params;
});

function handleTabClick(tab) {
  // 切换标签页时重置页码
  queryParams.value.pageIndex = 1;
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  tabRef.value.getList(processedQueryParams.value);
}

function resetQuery() {
  proxy.resetForm('queryRef');
  dateRange.value = [];
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  handleQuery();
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.tab-content {
  margin-top: 20px;
}

.mb8 {
  margin-bottom: 8px;
}
</style>
