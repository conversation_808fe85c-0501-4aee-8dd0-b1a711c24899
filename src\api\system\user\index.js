import request from '@/utils/request';

// 平台用户列表
export function listUser(params) {
  return request({
    url: '/platform/platformUser/index',
    method: 'post',
    data: params,
  });
}

// 获取平台用户详情
export function getUserDetail(id) {
  return request({
    url: `/platform/platformUser/query/${id}`,
    method: 'get',
  });
}

// 新增平台用户
export function addUser(data) {
  return request({
    url: '/platform/platformUser/add',
    method: 'post',
    data,
  });
}

// 编辑平台用户
export function updateUser(data) {
  return request({
    url: '/platform/platformUser/edit',
    method: 'post',
    data,
  });
}

// 更新用户角色
export function updateUserRole(data) {
  return request({
    url: '/platform/platformUser/updateUserRole',
    method: 'post',
    data,
  });
}

// 删除平台用户
export function delUser(shopUserId) {
  return request({
    url: '/platform/platformUser/delete?shopUserId=' + shopUserId,
    method: 'post',
  });
}
