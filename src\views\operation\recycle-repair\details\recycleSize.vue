<template>
  <div class="app-container">
    <div class="page-header">
      <h2>旧物回收订单详情</h2>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 旧物回收订单详情 -->
      <div class="info-section">
        <div class="section-title">旧物回收订单详情</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>订单编号：</label>
              <span>{{ orderDetail.orderNo || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>回收类别：</label>
              <span>{{ '旧物' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>订单重量：</label>
              <span>{{ orderDetail.weight || '--' }} 公斤</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>订单金额：</label>
              <span>{{ orderDetail.flowPrice || '--' }} 元</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>操作帐号昵称：</label>
              <span>{{ orderDetail.operationName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>网点名称：</label>
              <span>{{ orderDetail.websiteName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>驿站名称：</label>
              <span>{{ orderDetail.stationName || '--' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 会员信息 -->
      <div class="info-section">
        <div class="section-title">会员信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>会员帐号：</label>
              <span>{{ orderDetail.mobile || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>会员昵称：</label>
              <span>{{ orderDetail.nickName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>联系电话：</label>
              <span>{{ orderDetail.mobile || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>预估重量：</label>
              <span>{{ orderDetail.weight || '--' }} 公斤</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>下单时间：</label>
              <span>{{ parseTime(orderDetail.createTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>预约上门时间起：</label>
              <span>{{ parseTime(orderDetail.appointBeginTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>预约上门时间止：</label>
              <span>{{ parseTime(orderDetail.appointEndTime) || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="info-item">
              <label>详细地址：</label>
              <span>{{ getFullAddress() || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>完成时间：</label>
              <span>{{ parseTime(orderDetail.completeTime) || '--' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 订单信息 -->
      <div class="info-section">
        <div class="section-title">订单信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>回收类别：</label>
              <span>{{ '旧衣物' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>物品类型：</label>
              <span>{{ orderDetail.type || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>物品名称：</label>
              <span>{{ orderDetail.name || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>预估重量：</label>
              <span>{{ orderDetail.weight || '--' }} 公斤</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>尺寸：</label>
              <span>{{ orderDetail.recycleSize || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>成交金额：</label>
              <span>{{ orderDetail.price || '--' }} 元</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup name="RecycleSizeDetail">
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getRecycleSizeDetail } from '@/api/operation/recycleRepair';

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const loading = ref(true);
const showImageViewer = ref(false);
const previewImageUrl = ref('');
const orderDetail = ref({});

onMounted(() => {
  const orderId = route.query.id || route.params.id;
  if (orderId) {
    getOrderDetail(orderId);
  } else {
    proxy.$modal.msgError('缺少订单ID参数');
    goBack();
  }
});

function getOrderDetail(orderId) {
  loading.value = true;
  getRecycleSizeDetail(orderId)
    .then((response) => {
      orderDetail.value = response.data || {};
    })

    .finally(() => {
      loading.value = false;
    });
}

function getFullAddress() {
  const address = orderDetail.value.recycleAddress;
  if (!address) return '';

  const parts = [];
  if (address.detail) parts.push(address.detail);

  return parts.join(' ');
}

function previewImage(imageUrl) {
  previewImageUrl.value = imageUrl;
  showImageViewer.value = true;
}

function goBack() {
  router.go(-1);
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.detail-content {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.info-section {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
  padding-bottom: 8px;
  position: relative;
}

.section-title::after {
  content: '';
  width: 4px;
  height: 15px;
  background: #409eff;
  position: absolute;
  left: -8px;
  top: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.image-section {
  margin-bottom: 20px;
}

.image-title {
  font-weight: 500;
  color: #606266;
  margin-bottom: 10px;
  font-size: 14px;
}

.image-gallery {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.image-item {
  cursor: pointer;
  border: 2px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-item:hover {
  border-color: #409eff;
}
</style>
