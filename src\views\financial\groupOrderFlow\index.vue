<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
          @change="handleDateChange"
        />
      </el-form-item>

      <el-form-item label="(供货商)手机" prop="supplierMobile">
        <el-input
          v-model="queryParams.supplierMobile"
          placeholder="请输入供货商手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供货商绑定名称" prop="shopSupplierName">
        <el-input
          v-model="queryParams.shopSupplierName"
          placeholder="请输入供货商绑定名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="网点" prop="websiteId">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationId">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="groupOrderList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="团购订单编号"
        align="center"
        prop="orderNo"
        width="150"
      />
      <el-table-column label="团购订单标题" align="center" prop="productName" />
      <el-table-column
        label="订单状态"
        align="center"
        prop="orderStatus"
        width="100"
      >
        <template #default="scope">
          <dict-tag :options="order_status" :value="scope.row.orderStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="供应结算价(元)"
        align="center"
        prop="supplierMoney"
        width="150"
      >
        <template #default="scope">
          <span>{{ scope.row.supplierMoney || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="总结算价(元)"
        align="center"
        prop="payPrice"
        width="150"
      >
        <template #default="scope">
          <span>{{ scope.row.payPrice || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="完单时间"
        align="center"
        prop="receiptTime"
        width="120"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.receiptTime) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网点名称" align="center" prop="websiteName" />
      <el-table-column label="驿站名称" align="center" prop="stationName" />
      <el-table-column
        label="操作帐号昵称"
        align="center"
        prop="operatorNickname"
        width="120"
      />

      <el-table-column
        label="操作"
        align="center"
        width="100"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部统计信息 -->
    <div class="summary-info">
      累计销售笔数: {{ totalCount }}笔 累计供应商结算金额: ￥{{
        supplierAmount
      }}元 累计销售金额: ￥{{ salesAmount }}元
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="团购订单详情"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="detail-content">
        <!-- 团购订单信息 -->
        <div class="detail-section">
          <h3 class="section-title">团购订单信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">团购订单编号:</span>
              <span class="value">{{ detailData.orderNo || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">订单状态:</span>
              <span class="value">
                <dict-tag
                  :options="order_status"
                  :value="detailData.orderStatus"
                />
              </span>
            </div>
            <div class="detail-item">
              <span class="label">总结算价:</span>
              <span class="value">{{
                detailData.totalSettlementPrice
                  ? `￥${detailData.totalSettlementPrice}`
                  : '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="label">完单时间:</span>
              <span class="value">{{
                parseTime(detailData.completeTime) || '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="label">网点:</span>
              <span class="value">{{ detailData.websiteName || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">操作帐号:</span>
              <span class="value">{{ detailData.operatorAccount || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">手机:</span>
              <span class="value">{{ detailData.operatorPhone || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">驿站:</span>
              <span class="value">{{ detailData.stationName || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 团购订单供应商信息 -->
        <div class="detail-section">
          <h3 class="section-title">团购订单供应商信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">帐号:</span>
              <span class="value">{{ detailData.supplierAccount || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">手机:</span>
              <span class="value">{{ detailData.supplierMobile || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">绑定名称:</span>
              <span class="value">{{
                detailData.shopSupplierName || '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="label">手机:</span>
              <span class="value">{{ detailData.supplierMobile2 || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">供应结算价:</span>
              <span class="value">{{
                detailData.supplierSettlementPrice
                  ? `￥${detailData.supplierSettlementPrice}`
                  : '-'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 支付记录表格 -->
        <div class="detail-section">
          <h3 class="section-title">支付记录</h3>
          <el-table
            :data="paymentRecords"
            border
            style="width: 100%"
            max-height="400"
          >
            <el-table-column
              label="会员帐号"
              align="center"
              prop="memberAccount"
              width="120"
            />
            <el-table-column
              label="手机"
              align="center"
              prop="memberPhone"
              width="120"
            />
            <el-table-column
              label="订单时间"
              align="center"
              prop="orderTime"
              width="160"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.orderTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="预订数量"
              align="center"
              prop="preOrderQuantity"
              width="100"
            />
            <el-table-column
              label="预付金额"
              align="center"
              prop="prepaymentAmount"
              width="100"
            >
              <template #default="scope">
                <span>￥{{ scope.row.prepaymentAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="打款状态"
              align="center"
              prop="paymentStatus"
              width="100"
            >
              <template #default="scope">
                <dict-tag
                  :options="payment_status_dict"
                  :value="scope.row.paymentStatus"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="支付方式"
              align="center"
              prop="paymentMethod"
              width="100"
            />
            <el-table-column label="打款信息" align="center" width="200">
              <template #default="scope">
                <div class="payment-info">
                  <div>帐户: {{ scope.row.paymentAccount }}</div>
                  <div>账号: {{ scope.row.paymentAccountNo }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="发货数量"
              align="center"
              prop="deliveryQuantity"
              width="100"
            />
            <el-table-column
              label="结算(退/补)金额"
              align="center"
              prop="settlementAmount"
              width="120"
            >
              <template #default="scope">
                <span>￥{{ scope.row.settlementAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="打款状态"
              align="center"
              prop="finalPaymentStatus"
              width="100"
            >
              <template #default="scope">
                <dict-tag
                  :options="payment_status_dict"
                  :value="scope.row.finalPaymentStatus"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="打款时间"
              align="center"
              prop="paymentTime"
              width="160"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.paymentTime) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GroupOrderFlow">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { getGroupOrderFlowList } from '@/api/financial';

const { proxy } = getCurrentInstance();

const groupOrderList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);
const totalCount = ref();
const supplierAmount = ref();
const salesAmount = ref();
const dateRange = ref([]);

// 详情弹窗相关
const detailDialogVisible = ref(false);
const detailData = ref({});
const paymentRecords = ref([]);

// 字典数据
const {
  payment_status_dict,
  order_status,
  outlet_name_dict,
  station_name_dict,
} = proxy.useDict(
  'payment_status_dict',
  'order_status',
  'outlet_name_dict',
  'station_name_dict'
);

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    startTime: undefined,
    endTime: undefined,
    paymentStatus: undefined,
    supplierMobile: undefined,
    shopSupplierName: undefined,
    websiteName: undefined,
    stationName: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  getGroupOrderFlowList(queryParams.value)
    .then((response) => {
      groupOrderList.value = response.data?.page.records || [];
      total.value = response.data?.page?.total || 0;
      totalCount.value = response.data?.orderCount;
      salesAmount.value = response.data?.totalAmount;
    })

    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  dateRange.value = [];
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleDateChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.value.startTime = dates[0];
    queryParams.value.endTime = dates[1];
  } else {
    queryParams.value.startTime = undefined;
    queryParams.value.endTime = undefined;
  }
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}

function handleDetail(row) {
  // 模拟详情数据，实际应该调用API获取完整详情
  detailData.value = {
    ...row,
    operatorAccount: '张三',
    operatorPhone: '***********',
    supplierAccount: 'xsydn',
    supplierMobile: '***********',
    shopSupplierName: '重庆市渝北区仁丰食品有限公司',
    supplierMobile2: '***********',
  };

  // 模拟支付记录数据
  paymentRecords.value = [
    {
      memberAccount: 'xsydn2xsydn',
      memberPhone: '***********',
      orderTime: '2025-08-09 15:30:00',
      preOrderQuantity: 50,
      prepaymentAmount: 68.0,
      paymentStatus: '1',
      paymentMethod: '微信',
      paymentAccount: 'xsydn',
      paymentAccountNo: 'dfdsafdsafds34',
      deliveryQuantity: 3,
      settlementAmount: 50.0,
      finalPaymentStatus: '1',
      paymentTime: '2025-08-09 15:30:00',
    },
    {
      memberAccount: 'member2',
      memberPhone: '***********',
      orderTime: '2025-08-10 10:20:00',
      preOrderQuantity: 30,
      prepaymentAmount: 45.0,
      paymentStatus: '1',
      paymentMethod: '支付宝',
      paymentAccount: '钟庆伟',
      paymentAccountNo: '6227003788000071037',
      deliveryQuantity: 2,
      settlementAmount: 30.0,
      finalPaymentStatus: '1',
      paymentTime: '2025-08-10 10:20:00',
    },
    {
      memberAccount: 'member3',
      memberPhone: '***********',
      orderTime: '2025-08-11 14:15:00',
      preOrderQuantity: 20,
      prepaymentAmount: 30.0,
      paymentStatus: '1',
      paymentMethod: '银行卡',
      paymentAccount: '重庆玛银科技有限公司',
      paymentAccountNo: '5321432143214321434214234',
      deliveryQuantity: 1,
      settlementAmount: 20.0,
      finalPaymentStatus: '1',
      paymentTime: '2025-08-11 14:15:00',
    },
  ];

  detailDialogVisible.value = true;
}
</script>

<style scoped>
.summary-info {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.detail-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.detail-item .value {
  color: #303133;
  flex: 1;
}

.dialog-footer {
  text-align: center;
}

.payment-info {
  font-size: 12px;
  line-height: 1.4;
}

.payment-info div {
  margin-bottom: 2px;
}
</style>
