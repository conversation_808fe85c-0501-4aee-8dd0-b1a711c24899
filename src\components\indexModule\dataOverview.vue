<template>
    <div>
        <el-row class="w-full">
            <el-col :span="8" :xs="24">
                <h4 class="m-0">全厂能耗数据概览</h4>
            </el-col>
            <el-col :span="16" :xs="24" class="flex justify-end">
                <ul class="list-none flex text-sm m-0 p-0">
                    <li v-for="(item,index) in quarterData" :key="index" @click="quarterChange(index)" class="mx-4" :class="{'text-[#409eff]':index==quarterVlaue}"><a class="hover:text-[#409eff]">{{ item.name }}</a></li>
                </ul>
            </el-col>
        </el-row>
        <el-row class="w-full text-xs mt-4" :gutter="20">
            <el-col :xl="6" :xs="24" :md="12" class="text-[#333333] mb-4">
                <div class="p-3 py-5 shadow-sm shadow-[#d2d2d2] rounded-md">
                    <div class="opacity-80 text-sm h-6">全厂用水总量m<sup>3</sup></div>
                    <div class="flex items-center justify-around my-3 mt-3">
                        <el-statistic
                            group-separator=","
                            :precision="0"
                            :value="28425123"
                        ></el-statistic>
                        <el-image class="w-[50px] h-[50px] ml-3" src="src/assets/images/index/1.png"></el-image>
                    </div>
                     <div class="flex items-center mt-5">
                        <div class="flex-1 flex h-[26px] leading-[26px] text-base">同比： <el-image class="w-[26px] h-[26px]" src="src/assets/images/index/shang.png"></el-image>5.11%</div>
                        <div class="flex-1 flex h-[26px] leading-[26px] text-base">环比： <el-image class="w-[26px] h-[26px]" src="src/assets/images/index/xia.png"></el-image>5.11%</div>
                     </div>
                </div>
            </el-col>
            <el-col :xl="6" :xs="24" :md="12" class="text-[#333333] mb-4">
                <div class="p-3 py-5 shadow-sm shadow-[#d2d2d2] rounded-md">
                    <div class="opacity-80 text-sm h-6">全厂用电总量kWh</div>
                    <div class="flex items-center justify-around my-3 mt-3">
                        <el-statistic
                            group-separator=","
                            :precision="0"
                            :value="28425123"
                        ></el-statistic>
                        <el-image class="w-[50px] h-[50px] ml-3" src="src/assets/images/index/2.png"></el-image>
                    </div>
                     <div class="flex items-center mt-5">
                        <div class="flex-1 flex h-[26px] leading-[26px] text-base">同比： <el-image class="w-[26px] h-[26px]" src="src/assets/images/index/shang.png"></el-image>5.11%</div>
                        <div class="flex-1 flex h-[26px] leading-[26px] text-base">环比： <el-image class="w-[26px] h-[26px]" src="src/assets/images/index/xia.png"></el-image>5.11%</div>
                     </div>
                </div>
            </el-col>
            <el-col :xl="6" :xs="24" :md="12" class="text-[#333333] mb-4">
                <div class="p-3 py-5 shadow-sm shadow-[#d2d2d2] rounded-md">
                    <div class="opacity-80 text-sm h-6">全厂压缩空气使用量m<sup>3</sup></div>
                    <div class="flex items-center justify-around my-3 mt-3">
                        <el-statistic
                            group-separator=","
                            :precision="0"
                            :value="28425123"
                        ></el-statistic>
                        <el-image class="w-[50px] h-[50px] ml-3" src="src/assets/images/index/3.png"></el-image>
                    </div>
                     <div class="flex items-center mt-5">
                        <div class="flex-1 flex h-[26px] leading-[26px] text-base">同比： <el-image class="w-[26px] h-[26px]" src="src/assets/images/index/shang.png"></el-image>5.11%</div>
                        <div class="flex-1 flex h-[26px] leading-[26px] text-base">环比： <el-image class="w-[26px] h-[26px]" src="src/assets/images/index/xia.png"></el-image>5.11%</div>
                     </div>
                </div>
            </el-col>
            <el-col :xl="6" :xs="24" :md="12" class="text-[#333333] mb-4">
                <div class="p-3 py-5 shadow-sm shadow-[#d2d2d2] rounded-md">
                    <div class="opacity-80 text-sm h-6">全厂天然气使用量m<sup>3</sup></div>
                    <div class="flex items-center justify-around my-3 mt-3">
                        <el-statistic
                            group-separator=","
                            :precision="0"
                            :value="28425123"
                        ></el-statistic>
                        <el-image class="w-[50px] h-[50px] ml-3" src="src/assets/images/index/4.png"></el-image>
                    </div>
                     <div class="flex items-center mt-5">
                        <div class="flex-1 flex h-[26px] leading-[26px] text-base">同比： <el-image class="w-[26px] h-[26px]" src="src/assets/images/index/shang.png"></el-image>5.11%</div>
                        <div class="flex-1 flex h-[26px] leading-[26px] text-base">环比： <el-image class="w-[26px] h-[26px]" src="src/assets/images/index/xia.png"></el-image>5.11%</div>
                     </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
const quarterData = [ 
    { name: '今日', value: 0 },
    { name: '本周', value: 1 },
    { name: '本月', value: 2 },
    { name: '本季度', value: 3 },
    { name: '年度', value: 4 },
 ];
 const quarterVlaue = ref(4); // 默认显示年度
 function quarterChange(value) {
     quarterVlaue.value = value;
 }
</script>

<style scoped>

</style>
