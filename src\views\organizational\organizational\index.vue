<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="级别" prop="level">
        <el-select
          v-model="queryParams.level"
          placeholder="请选择级别"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in mobile_user_level"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="网点" prop="websiteName">
        <RemoteSelect
          v-model="queryParams.websiteId"
          url="/platform/user/website"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择网点"
          responsePath="data"
          clearable
          class="w-[200px]"
          @change="queryParams.stationId = null"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="驿站" prop="stationName">
        <RemoteSelect
          v-model="queryParams.stationId"
          :url="`/platform/user/station`"
          :extraParamsArr="['websiteId']"
          :extraParams="{
            websiteId: queryParams.websiteId,
          }"
          value-key="user_id"
          label-key="bind_name"
          keyword-key="name"
          placeholder="请选择驿站"
          responsePath="data"
          clearable
          class="w-[200px]"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="账号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入账号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入地址"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column label="账号" align="center" prop="mobile" />
      <el-table-column label="名称" align="center" prop="nickname" />
      <el-table-column
        label="地址"
        align="center"
        prop="address"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="级别" align="center" prop="level">
        <template #default="scope">
          <dict-tag :options="mobile_user_level" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column
        label="所属网点名称"
        align="center"
        prop="websiteName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="所属驿站名称"
        align="center"
        prop="stationName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="注册时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="160"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleAudit(scope.row)"
            >审核</el-button
          >
          <el-button link type="primary" @click="handleRole(scope.row)"
            >角色</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 角色管理对话框 -->
    <el-dialog title="角色管理" v-model="roleOpen" width="600px" append-to-body>
      <el-form ref="roleRef" :model="roleForm" label-width="100px">
        <div class="user-info-section">
          <div class="section-title">基础信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="账号">
                <span>{{ roleForm.mobile || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="名称">
                <span>{{ roleForm.nickname || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="绑定名称">
                <span>{{ roleForm.stationName || '--' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="role-section">
          <div class="section-title">角色权限</div>
          <el-form-item label="选择角色">
            <el-checkbox-group v-model="roleForm.roleIds">
              <el-checkbox
                v-for="role in roleOptions"
                :key="role.roleId"
                :label="role.roleId"
              >
                {{ role.roleName }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer is-center">
          <el-button
            type="primary"
            :loading="roleSubmitLoading"
            @click="submitRoleForm"
          >
            确 定
          </el-button>
          <el-button @click="cancelRole">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      title="用户审核"
      v-model="auditOpen"
      width="500px"
      append-to-body
    >
      <el-form ref="auditRef" :model="auditForm" label-width="80px">
        <el-form-item label="代理商ID">
          <span>{{ auditForm.agentId }}</span>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="0">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入审核备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer is-center">
          <el-button
            type="primary"
            :loading="auditSubmitLoading"
            @click="submitAuditForm"
          >
            确 定
          </el-button>
          <el-button @click="cancelAudit">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OrganizationalUser">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  listOrganizationalUser,
  getOrganizationalRoles,
  updateOrganizationalUserRole,
  getOrganizationalUserDetail,
} from '@/api/organizational/organizational';
const router = useRouter();
const { proxy } = getCurrentInstance();
const { mobile_user_level } = proxy.useDict('mobile_user_level');

const userList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 角色管理相关
const roleOpen = ref(false);
const roleSubmitLoading = ref(false);
const roleOptions = ref([]);
const roleForm = reactive({
  userId: undefined,
  agentId: '',
  mobile: '',
  nickname: '',
  stationName: '',
  roleIds: [],
});

// 审核相关
const auditOpen = ref(false);
const auditSubmitLoading = ref(false);
const auditForm = reactive({
  userId: undefined,
  agentId: '',
  status: 1,
  remark: '',
});

const data = reactive({
  queryParams: {
    pageIndex: 1, // 页码，默认为1
    pageSize: 10, // 页大小，默认为10
    startIndex: 0, // 开始条数
    websiteId: undefined, // 网点ID
    stationId: undefined, // 驿站ID
    agentId: undefined, // 代理商ID
    websiteName: undefined, // 网点名称
    stationName: undefined, // 驿站名称
    level: undefined, // 级别
    mobile: undefined, // 手机号
    nickname: undefined, // 名称
    address: undefined, // 地址
    keyword: undefined, // 搜索字符串
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
  getRoleOptions();
});

function getRoleOptions() {
  getOrganizationalRoles()
    .then((response) => {
      roleOptions.value = response.data || [];
    })
    .catch((error) => {
      console.error('获取角色列表失败:', error);
    });
}

function getList() {
  loading.value = true;
  // 计算startIndex
  queryParams.value.startIndex =
    (queryParams.value.pageIndex - 1) * queryParams.value.pageSize;

  listOrganizationalUser(queryParams.value)
    .then((response) => {
      userList.value = response.data?.records || [];
      total.value = response.data?.total || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

// 角色管理相关函数
function handleRole(row) {
  getOrganizationalUserDetail(row.userId).then((res) => {
    roleForm.userId = res.data.userId;
    roleForm.mobile = res.data.mobile;
    roleForm.nickname = res.data.nickname;
    roleForm.stationName = res.data.stationName;
    roleForm.roleIds = res.data.roleIds?.map((item) => item.roleId) || [];
    roleOpen.value = true;
  });
}

function cancelRole() {
  roleOpen.value = false;
  roleForm.userId = undefined;
  roleForm.agentId = '';
  roleForm.mobile = '';
  roleForm.nickname = '';
  roleForm.stationName = '';
  roleForm.roleIds = [];
}

function submitRoleForm() {
  if (!roleForm.roleIds || roleForm.roleIds.length === 0) {
    proxy.$modal.msgWarning('请至少选择一个角色');
    return;
  }

  roleSubmitLoading.value = true;
  updateOrganizationalUserRole({
    userId: roleForm.userId,
    roleIds: roleForm.roleIds,
  })
    .then((response) => {
      if (response.code == 1) {
        proxy.$modal.msgSuccess('角色更新成功');
        roleOpen.value = false;
        getList(); // 刷新列表以显示更新后的角色
      } else {
        proxy.$modal.msgError(response.msg || '角色更新失败');
      }
    })
    .catch((error) => {
      proxy.$modal.msgError('角色更新失败');
      console.error('角色更新失败:', error);
    })
    .finally(() => {
      roleSubmitLoading.value = false;
    });
}

// 审核相关函数
function handleAudit(row) {
  router.push({
    path: '/organizational/organizational/audit/' + row.userId,
  });
}

function cancelAudit() {
  auditOpen.value = false;
  auditForm.userId = undefined;
  auditForm.agentId = '';
  auditForm.status = 1;
  auditForm.remark = '';
}
</script>

<style scoped>
.user-info-section,
.role-section {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.section-title {
  font-weight: 600;
  color: #303133;
  margin: -4px 0 12px;
  font-size: 14px;
}

.dialog-footer.is-center {
  display: flex;
  justify-content: center;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.el-checkbox {
  margin-right: 0;
}
</style>
