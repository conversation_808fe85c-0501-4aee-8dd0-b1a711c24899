// 财务数据模块通用样式
.financial-container {
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .filter-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .help-info {
      margin-left: 20px;
    }
  }

  .payment-info {
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .summary-info {
      color: #606266;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .el-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    th {
      background-color: #f5f7fa;
      font-weight: 600;
    }
  }

  .el-form-item {
    margin-bottom: 16px;
  }

  .el-button {
    border-radius: 6px;
  }
} 