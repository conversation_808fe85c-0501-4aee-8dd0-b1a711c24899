<template>
  <div class="app-container">
    <div class="page-header">
      <h2>注册用户信息</h2>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div v-loading="loading" class="audit-content">
      <!-- 注册用户信息 -->
      <div class="info-section">
        <div class="section-title">注册用户信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>帐号：</label>
              <span>{{ userInfo.mobile || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>昵称：</label>
              <span>{{ userInfo.nickname || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>手机号：</label>
              <span>{{ userInfo.mobile || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>注册时间：</label>
              <span>{{ parseTime(userInfo.createTime) || '--' }}</span>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="info-item">
              <label>主副类型：</label>
              <dict-tag :options="yes_or_no" :value="userInfo.isMaster" />
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 级别与所属关系 -->
      <div class="info-section">
        <div class="section-title">级别与所属关系</div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="avatar-section">
              <el-avatar :size="80" :src="userInfo.avatarurl">
                <el-icon><User /></el-icon>
              </el-avatar>
            </div>
          </el-col>
          <el-col :span="18">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>网点：</label>
                  <RemoteSelect
                    v-model="userInfo.websiteId"
                    url="/platform/user/website"
                    value-key="user_id"
                    label-key="bind_name"
                    keyword-key="name"
                    placeholder="请选择网点"
                    responsePath="data"
                    clearable
                    class="w-[200px]"
                    @change="userInfo.stationId = null"
                  />
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>驿站：</label>
                  <RemoteSelect
                    v-model="userInfo.stationId"
                    :url="`/platform/user/station`"
                    :extraParamsArr="['websiteId']"
                    :extraParams="{
                      websiteId: userInfo.websiteId,
                    }"
                    value-key="user_id"
                    label-key="bind_name"
                    keyword-key="name"
                    placeholder="请选择驿站"
                    responsePath="data"
                    clearable
                    class="w-[200px]"
                  />
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mt-[15px]">
              <el-col :span="8">
                <div class="info-item">
                  <label>级别：</label>
                  <el-select v-model="userInfo.level" class="w-[200px]">
                    <el-option
                      v-for="item in mobile_user_level"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>编号：</label>
                  <span>{{ userInfo.agentId || '--' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>所属关系：</label>
                  <span
                    >{{ userInfo.agentName }}->{{ userInfo.websiteName }}->{{
                      userInfo.stationName
                    }}</span
                  >
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>运营状态：</label>
                  <el-select v-model="userInfo.operationState">
                    <el-option
                      v-for="item in operating_status"
                      :key="parseInt(item.value)"
                      :label="item.label"
                      :value="parseInt(item.value)"
                    />
                  </el-select>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>

      <!-- 绑定信息 -->
      <div class="info-section">
        <div class="section-title">绑定信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>名称：</label>
              <span>{{ userInfo.bindName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="info-item">
              <label>地址：</label>
              <span>{{ userInfo.address || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>负责人：</label>
              <span>{{ userInfo.bindBy || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>负责人手机号：</label>
              <span>{{ userInfo.bindByPhone || '--' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 照片 -->
      <div class="info-section">
        <div class="section-title">照片</div>
        <div class="photo-gallery">
          <div
            v-for="(photo, index) in userInfo.images"
            :key="index"
            class="photo-item"
            @click="previewImage(photo.filePath)"
          >
            <el-image
              :src="photo.filePath"
              fit="cover"
              style="width: 120px; height: 80px; border-radius: 4px"
            />
          </div>
        </div>
        <div class="photo-desc">
          第1张图为门头照片,后面为营业执照(公司/个体)或身份证正反面(个人)照片
        </div>
      </div>

      <!-- 个人服务信息 -->
      <div v-if="isPersonalService" class="info-section">
        <div class="section-title">若为个人服务，请审核如下信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>职业技能：</label>
              <span>{{ userInfo.vocationalSkills || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>工作经验：</label>
              <span>{{ userInfo.experience || '--' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-[15px]">
          <el-col :span="12">
            <div class="info-item">
              <label>职业擅长：</label>
              <span>{{ userInfo.vocationalForte || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>备注：</label>
              <span>{{ userInfo.createRemark || '--' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-[15px]">
          <el-col :span="8">
            <div class="info-item">
              <label>证书名称：</label>
              <span>{{ userInfo.certificate || '--' }}</span>
            </div>
          </el-col>

          <el-col :span="16">
            <div class="info-item">
              <label>证书照片：</label>
              <div
                v-for="(photo, index) in userInfo.images"
                :key="index"
                class="photo-item"
                @click="previewImage(photo.filePath)"
              >
                <el-image
                  :src="photo.filePath"
                  fit="cover"
                  class="w-[120px] h-[80px] rounded-[4px]"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="info-section">
        <div class="section-title">关联区域</div>
        <el-row :gutter="20" class="mt-[15px]">
          <el-col :span="8">
            <div class="info-item">
              <label>网点：</label>
              <RemoteSelect
                v-model="websiteId"
                url="/platform/user/website"
                value-key="user_id"
                label-key="bind_name"
                keyword-key="name"
                placeholder="请选择网点"
                responsePath="data"
                clearable
                class="w-[200px]"
                @change="userInfo.stationIds = null"
              />
            </div>
          </el-col>
          <el-col :span="16">
            <div class="info-item">
              <label>关联区域：</label>
              <RemoteSelect
                v-model="userInfo.stationIds"
                :url="`/platform/user/station`"
                multiple
                :extraParamsArr="['websiteId']"
                :extraParams="{
                  websiteId: websiteId,
                }"
                value-key="user_id"
                label-key="bind_name"
                keyword-key="name"
                placeholder="请选择关联区域"
                responsePath="data"
                clearable
                class="w-[200px]"
              />
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- 审核状态 -->
      <div class="info-section">
        <div class="section-title">审核状态</div>
        <el-form ref="auditFormRef" :model="auditForm" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="审核结果">
                <el-radio-group v-model="auditForm.state">
                  <el-radio :label="2">通过</el-radio>
                  <el-radio :label="3">拒绝</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="审核备注">
                <el-input
                  v-model="auditForm.remark"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入审核备注"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" :loading="submitLoading" @click="submitAudit">
          提交审核
        </el-button>
        <el-button @click="goBack">取消</el-button>
      </div>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImageViewer"
      :url-list="[previewImageUrl]"
      @close="showImageViewer = false"
    />
  </div>
</template>

<script setup name="UserAudit">
import { ref, reactive, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  getOrganizationalUserDetail,
  auditUser,
} from '@/api/organizational/organizational';
import { User } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const { mobile_user_level, yes_or_no, operating_status } = proxy.useDict(
  'mobile_user_level',
  'yes_or_no',
  'operating_status'
);

const loading = ref(true);
const submitLoading = ref(false);
const showImageViewer = ref(false);
const previewImageUrl = ref('');

const userInfo = ref({});
const auditForm = reactive({
  userId: '',
  state: 1, // 审核结果
  remark: '', // 审核备注
});
const websiteId = ref('');

// 判断是否为个人服务
const isPersonalService = computed(() => {
  return true; // 假设2为个人服务类型
});

onMounted(() => {
  const userId = route.params.id;
  if (userId) {
    getUserDetail(userId);
  }
});

function getUserDetail(userId) {
  loading.value = true;
  getOrganizationalUserDetail(userId)
    .then((response) => {
      userInfo.value = response.data;
      auditForm.userId = response.data.userId;
      // 处理照片数组
      if (response.data.photos && typeof response.data.photos === 'string') {
        userInfo.value.photos = response.data.photos.split(',');
      } else if (Array.isArray(response.data.photos)) {
        userInfo.value.photos = response.data.photos;
      } else {
        userInfo.value.photos = [];
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function getLevelName(level) {
  const levelItem = mobile_user_level.value.find(
    (item) => parseInt(item.value) === level
  );
  return levelItem ? levelItem.label : '--';
}

function previewImage(imageUrl) {
  previewImageUrl.value = imageUrl;
  showImageViewer.value = true;
}

function submitAudit() {
  if (!auditForm.remark.trim()) {
    proxy.$modal.msgWarning('请填写审核备注');
    return;
  }

  submitLoading.value = true;
  auditUser({
    userId: auditForm.userId,
    state: auditForm.state,
    remark: auditForm.remark,
    operationState: userInfo.value.operationState,
    level: userInfo.value.level,
    websiteId: userInfo.value.websiteId,
    stationId: userInfo.value.stationId,
    stationIds: userInfo.value.stationIds,
  })
    .then((response) => {
      if (response.code === 1) {
        proxy.$modal.msgSuccess('审核提交成功');
        goBack();
      } else {
        proxy.$modal.msgError(response.msg || '审核提交失败');
      }
    })
    .catch((error) => {
      proxy.$modal.msgError('审核提交失败');
      console.error('审核提交失败:', error);
    })
    .finally(() => {
      submitLoading.value = false;
    });
}

function goBack() {
  router.go(-1);
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.audit-content {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.info-section {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
  position: relative;
  padding-bottom: 8px;
  &::after {
    content: '';
    width: 4px;
    height: 15px;
    background: #409eff;
    position: absolute;
    left: -8px;
    top: 4px;
  }
}

.info-item {
  display: flex;
  align-items: center;

  gap: 12px;
  height: 100%;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.avatar-section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.user-auth {
  margin-top: 15px;
}

.auth-status {
  display: flex;
  align-items: center;
  color: #67c23a;
  font-size: 14px;
}

.auth-dot {
  width: 8px;
  height: 8px;
  background: #67c23a;
  border-radius: 50%;
  margin-right: 8px;
}

.photo-gallery {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.photo-item {
  cursor: pointer;
  border: 2px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
  transition: border-color 0.3s;
}

.photo-item:hover {
  border-color: #409eff;
}

.photo-desc {
  color: #909399;
  font-size: 12px;
  margin-top: 10px;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.action-buttons .el-button {
  margin: 0 10px;
  min-width: 100px;
}

.el-form-item {
  margin-bottom: 15px;
}

.el-select,
.el-input {
  width: 100%;
}
</style>
