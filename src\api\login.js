import request from '@/utils/request';

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid,
  };
  return request({
    url: '/platform/passport/login',
    headers: {
      isToken: false,
      repeatSubmit: false,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    method: 'post',
    data: data,
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/platform/platformUser/getUserInfo',
    method: 'post',
  });
}

// 退出方法
export function logout() {
  return request({
    url: '/platform/passport/logout',
    method: 'post',
  });
}

// 获取当前登录人的路由
export const getRouters = () => {
  return request({
    url: '/platform/platformUser/getRoleList',
    method: 'post',
  });
};
