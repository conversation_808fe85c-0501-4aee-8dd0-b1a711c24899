<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="unset"
    >
      <el-form-item label="时间段" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          format="YYYY-MM"
          value-format="YYYY-MM"
          style="width: 240px"
          @change="handleTimeChange"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >确定</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button type="default" @click="handleSettlement"
          >查看结算情况</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="agentProfitList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="帐号" align="center" prop="account" width="120" />
      <el-table-column label="手机号" align="center" prop="phone" width="120" />
      <el-table-column
        label="昵称"
        align="center"
        prop="nickname"
        width="120"
      />
      <el-table-column label="级别" align="center" prop="level" width="100">
        <template #default="scope">
          <dict-tag :options="level_dict" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column
        label="网点名称"
        align="center"
        prop="outletName"
        width="200"
      />
      <el-table-column
        label="驿站名称"
        align="center"
        prop="stationName"
        width="200"
      />
      <el-table-column
        label="分润合计(元)"
        align="center"
        prop="totalProfit"
        width="120"
      >
        <template #default="scope">
          <span>￥{{ scope.row.totalProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="旧衣物订单累计分润(元)"
        align="center"
        prop="recycleProfit"
        width="180"
      >
        <template #default="scope">
          <span>￥{{ scope.row.recycleProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="团购订单累计分润(元)"
        align="center"
        prop="groupProfit"
        width="180"
      >
        <template #default="scope">
          <span>￥{{ scope.row.groupProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="商城订单累计分润金额(元)"
        align="center"
        prop="mallProfit"
        width="200"
      >
        <template #default="scope">
          <span>￥{{ scope.row.mallProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="批发商城订单累计分润(元)"
        align="center"
        prop="wholesaleProfit"
        width="200"
      >
        <template #default="scope">
          <span>￥{{ scope.row.wholesaleProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="合作商年费订单累计分润(元)"
        align="center"
        prop="partnerFeeProfit"
        width="220"
      >
        <template #default="scope">
          <span>￥{{ scope.row.partnerFeeProfit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="100"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部统计信息 -->
    <div class="summary-info">累计分润: ￥{{ totalProfit }}元</div>
  </div>
</template>

<script setup name="AgentProfitSharingStats">
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from 'vue';
import { getAgentProfitSharingStats } from '@/api/financial';

const { proxy } = getCurrentInstance();

const agentProfitList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);
const totalProfit = ref(2235);
const timeRange = ref([]);

// 字典数据
const { level_dict } = proxy.useDict('level_dict');

const data = reactive({
  queryParams: {
    pageIndex: 1,
    pageSize: 10,
    startMonth: undefined,
    endMonth: undefined,
  },
});

const { queryParams } = toRefs(data);

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;
  getAgentProfitSharingStats(queryParams.value)
    .then((response) => {
      agentProfitList.value = response.data?.records || [];
      total.value = response.data?.total || 0;
    })
    .catch((error) => {
      proxy.$modal.msgError('获取数据失败');
      console.error('获取数据失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageIndex = 1;
  getList();
}

function resetQuery() {
  timeRange.value = [];
  queryParams.value.startMonth = undefined;
  queryParams.value.endMonth = undefined;
  handleQuery();
}

function handleTimeChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.value.startMonth = dates[0];
    queryParams.value.endMonth = dates[1];
  } else {
    queryParams.value.startMonth = undefined;
    queryParams.value.endMonth = undefined;
  }
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
}

function handleDetail(row) {
  // 跳转到详情页面
  proxy.$router.push({
    path: '/financial/agentProfitSharingStats/details',
    query: { id: row.id },
  });
}

function handleSettlement() {
  proxy.$modal.msgInfo('查看结算情况功能');
}
</script>

<style scoped>
.summary-info {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
</style>
