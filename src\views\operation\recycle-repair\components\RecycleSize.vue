<template>
  <div class="recycle-size">
    <el-table
      v-loading="loading"
      :data="recycleList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单编号" align="center" prop="orderNo" />
      <el-table-column label="物品类型" align="center" prop="type" />
      <el-table-column label="物品名称" align="center" prop="name" />
      <el-table-column
        label="操作账号昵称"
        align="center"
        prop="operationName"
      />
      <el-table-column label="驿站名称" align="center" prop="stationName" />
      <el-table-column label="网点名称" align="center" prop="websiteName" />
      <el-table-column
        label="下单时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="完单时间"
        align="center"
        prop="completeTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.completeTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="currentPage"
      v-model:limit="currentPageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script setup name="RecycleSize">
import { ref, getCurrentInstance, watch } from 'vue';
import { listRecycleSize } from '@/api/operation/recycleRepair';

const { proxy } = getCurrentInstance();
const router = useRouter();
const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['refresh']);

const recycleList = ref([]);
const loading = ref(true);
const ids = ref([]);
const total = ref(0);
const currentPage = ref(1);
const currentPageSize = ref(10);
defineExpose({
  getList,
});

onMounted(() => {
  getList();
});
function getList(newParams) {
  loading.value = true;
  const params = {
    ...(newParams || props.queryParams),
    pageIndex: currentPage.value,
    pageSize: currentPageSize.value,
  };

  listRecycleSize(params)
    .then((response) => {
      recycleList.value = response.data?.records || [];
      total.value = response.data?.total || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handlePagination(pagination) {
  currentPage.value = pagination.page;
  currentPageSize.value = pagination.limit;
  getList();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
}

function handleView(row) {
  router.push({
    path: '/operation/recycle-repair/details/recycleSize/' + row.id,
  });
}
</script>

<style scoped>
.recycle-size {
  padding: 20px 0;
}
</style>
