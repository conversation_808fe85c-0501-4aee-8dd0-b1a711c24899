import request from '@/utils/request';

// 合作商入驻列表
export function listCooperation(params) {
  return request({
    url: '/platform/user/getRecycleAndRepairPartners  ',
    method: 'post',
    data: params,
  });
}

// 入驻申请列表
export function listCooperationApplication(params) {
  return request({
    url: '/platform/PartnerEnter/list',
    method: 'get',
    params,
  });
}

// 处理入驻申请
export function processEnterApplication(id) {
  return request({
    url: `/platform/PartnerEnter/processEnterApplication?id=${id}`,
    method: 'post',
  });
}

// 获取合作商详情
export function getPartnerDetail(userId) {
  return request({
    url: `/platform/user/getPartnerDetail?userId=${userId}`,
    method: 'get',
  });
}

// 续费订单信息
export function addPartnerOrder(data) {
  return request({
    url: `/platform/partnerrenewal/createPartnerRenewal`,
    method: 'post',
    data,
  });
}
