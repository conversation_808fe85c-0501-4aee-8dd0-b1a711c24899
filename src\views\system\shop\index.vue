<template>
  <div class="app-container" v-loading="loading">
    <iframe
      :src="iframeUrl"
      id="myIframe"
      frameborder="0"
      @load="loading = false"
      class="iframe-container"
    ></iframe>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
// 商城分类
const path = '/product/category/index';
const iframeUrl = ref(import.meta.env.VITE_APP_SHOP + path);
const loading = ref(true);

// 消息处理函数
const handleMessage = (event) => {
  if (event.data.type === 'load_success') {
    sendMessageToIframe();
  }
};

// 发送验证密码
function sendMessageToIframe() {
  const iframe = document.getElementById('myIframe');
  if (iframe && iframe.contentWindow) {
    iframe.contentWindow.postMessage(
      {
        type: 'USER',
        data: {
          params: JSON.parse(localStorage.getItem('loginInfo') || '{}'),
          path: path,
        },
      },
      '*'
    );
  }
}

onMounted(() => {
  // 监听iframe发送的消息
  window.addEventListener('message', handleMessage);
});

onUnmounted(() => {
  // 组件注销时清除事件监听器
  window.removeEventListener('message', handleMessage);
});
</script>

<style scoped>
.app-container {
  width: 100%;
  height: calc(100vh - 145px);
}
.iframe-container {
  width: 100%;
  height: 100%;
}
</style>
